<?php

use App\Http\Controllers\CustomerController;
use App\Http\Controllers\InteractionController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\PicklistController;
use App\Http\Controllers\PricelistController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\PurchaseOrderController;
use App\Http\Controllers\QuoteController;
use App\Http\Controllers\RapportController;
use App\Http\Controllers\SettingsController;
use App\Http\Controllers\TransmissionCostController;
use App\Http\Controllers\TransportPriceAgreementController;
use App\Http\Controllers\TransportPricelistController;
use App\Http\Controllers\TransusController;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Storage;
use Illuminate\Http\Request;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/storage/{filename}', function ($filename)
{
    return Storage::response("public/$filename");
});

Route::middleware([
    'auth:sanctum',
    config('jetstream.auth_session'),
    'verified'
])->group(function () {
    Route::get('bestellingen', [CustomerController::class, 'orders'])->name('bestellingen');
    Route::get('bestelling/{order}', [CustomerController::class, 'customerOrder'])->name('bestelling');
    Route::get('bestellingen/nieuw', [CustomerController::class, 'newOrder'])->name('customer.new.order');
    Route::get('livewire/customer-portal-orders', [App\Livewire\CustomerPortalOrders::class, 'getOrders'])->name('livewire.customer-portal-orders.get-orders');
});
Route::middleware([
    'auth:sanctum',
    config('jetstream.auth_session'),
    'verified',
    'not.customer'
])->group(function () {
    Route::get('/dashboard', function () {
        return view('dashboard');
    })->name('dashboard');

    Route::get('checkinvoices', [OrderController::class, 'checkInvoices']);
    Route::get('createhornbachinvoice/{invoice}', [OrderController::class, 'createHornbachTransusInvoice']);
    Route::get('createwelkoopinvoice/{invoice}', [OrderController::class, 'createWelkoopTransusInvoice']);
    Route::get('invoicehornbach', [OrderController::class, 'sendHornbachInvoices']);
    Route::get('parsecomlogs', [OrderController::class, 'parseComlogs']);

    //------------------CUSTOMER---------------//
    Route::get('customer', [CustomerController::class, 'index'])->name('customer.index');
    Route::get('customer/edit/{customer}', [CustomerController::class, 'edit'])->name('customer.edit');
    Route::get('customer/new', [CustomerController::class, 'new'])->name('customer.new');
    Route::post('customer/new', [CustomerController::class, 'save'])->name('customer.save');
    Route::post('/customer/{id}', [CustomerController::class, 'update'])->name('customer.update');
    Route::get('livewire/customer-table/get-customers', [App\Livewire\CustomerTable::class, 'getCustomers'])->name('livewire.customer-table.get-customers');
    Route::get('customer/prices/{customer}', [CustomerController::class, 'prices'])->name('customer.prices');
    Route::get('livewire/customer-orders/{customerId}', [App\Livewire\CustomerOrders::class, 'getOrders'])->name('livewire.customer-orders.get-orders');


    //------------------CUSTOMER COMMUNICATION---------------//
    Route::get('communication/{customer}', [InteractionController::class, 'view'])->name('communication.view');

    //------------------PRODUCT---------------//
    Route::get('product', [ProductController::class, 'index'])->name('product.index');
    Route::get('livewire/product-table/get-products', [App\Livewire\ProductTable::class, 'getProducts'])->name('livewire.product-table.get-products');
    Route::get('product/edit/{product}', [ProductController::class, 'edit'])->name('product.edit');
    Route::post('/product/{id}', [ProductController::class, 'update'])->name('product.update');
    Route::get('livewire/customer-prices-table/get-customers/{product_id}', [App\Http\Livewire\CustomerPricesTable::class, 'getCustomerPrices'])->name('livewire.customer-prices-table.get-customer-prices');
    Route::get('basisproducten', [ProductController::class, 'base'])->name('product.base');
    Route::get('livewire/base-product-table/get-products', [App\Livewire\BaseProductTable::class, 'getProducts'])->name('livewire.base-product-table.get-products');
    Route::get('basisproducten/edit/{baseProduct}', [ProductController::class, 'baseProductEdit'])->name('baseproduct.edit');
    Route::get('product/new', [ProductController::class, 'newProduct'])->name('product.new');
    //----WOO PRODUCT----//
    Route::get('wooproducten', [ProductController::class, 'wooProducts'])->name('product.wooproducten');
    Route::get('livewire/product-woo-table/get-products', [App\Livewire\ProductWooTable::class, 'getProducts'])->name('livewire.product-woo-table.get-products');

    //------------------PRICELIST---------------//
    Route::get('pricelists', [PricelistController::class, 'index'])->name('pricelist.index');
    Route::get('pricelists/edit/{pricelist}', [PricelistController::class, 'edit'])->name('pricelist.edit');
    Route::get('pricelists/print/{pricelist}', [PricelistController::class, 'print'])->name('pricelist.print');
    Route::get('livewire/pricelist-table/get-pricelists', [App\Livewire\PricelistTable::class, 'getPricelists'])->name('livewire.pricelist-table.get-pricelists');
    //-----TRANSPORT PRICELISTS------//
    Route::get('transportpricelists', [TransportPricelistController::class, 'transportPricelists'])->name('transportpricelists.index');
    Route::get('transportpricelists/edit/{pricelist}', [TransportPricelistController::class, 'editTransportPricelist'])->name('transportpricelists.edit');
    Route::get('livewire/transport-pricelist-table/get-pricelists', [App\Livewire\TransportPricelistTable::class, 'getPricelists'])->name('livewire.transport-pricelist-table.get-pricelists');

    //------------------TRANSPORT PRICE AGREEMENT---------------//
    Route::get('transportagreement/new/{customer}', [TransportPriceAgreementController::class, 'new'])->name('transportagreement.new');
    Route::get('transportagreement/edit/{transportPriceAgreement}', [TransportPriceAgreementController::class, 'edit'])->name('transportagreement.edit');
    Route::post('transportagreement/save/{customer}', [TransportPriceAgreementController::class, 'save'])->name('transportagreement.save');
    Route::post('transportagreement/update/{transportPriceAgreement}', [TransportPriceAgreementController::class, 'update'])->name('transportagreement.update');

    //------------------ORDERS---------------//
    Route::get('order', [OrderController::class, 'index'])->name('order.index');
    Route::get('finished-order', [OrderController::class, 'finishedOrders'])->name('order.finished');
    Route::get('unsent-order', [OrderController::class, 'unsentOrders'])->name('order.unsent');
    Route::get('all-order', [OrderController::class, 'allOrders'])->name('order.all');
    Route::get('order/edit/{order}', [OrderController::class, 'edit'])->name('order.edit');
    Route::get('order/credit/{order}', [OrderController::class, 'credit'])->name('order.credit');
    Route::get('livewire/order-table/get-orders', [App\Livewire\OrderTable::class, 'getOrders'])->name('livewire.order-table.get-orders');
    Route::get('livewire/order-finished-table/get-orders', [App\Livewire\OrderFinishedTable::class, 'getOrders'])->name('livewire.order-finished-table.get-orders');
    Route::get('livewire/order-all-table/get-orders', [App\Livewire\OrderAllTable::class, 'getOrders'])->name('livewire.order-all-table.get-orders');
    Route::get('livewire/order-unsent/get-orders', [App\Livewire\OrderUnsent::class, 'getOrders'])->name('livewire.order-unsent.get-orders');
    Route::get('order/new', [OrderController::class, 'new'])->name('order.new');
    Route::get('orders/print/{order}', [OrderController::class, 'print'])->name('order.print');
    Route::get('testorder', [OrderController::class, 'getOrder']);
    Route::get('createtransusmessage/{order}', [OrderController::class, 'createTransusMessage']);
    Route::get('recalculateorders', [OrderController::class, 'recalculateOrders']);
    Route::get('checkshipment/{order}', [OrderController::class, 'checkShipmentStatus']);
    Route::get('checkshipments', [OrderController::class, 'checkShipments']);
    Route::get('parseinvoice', [OrderController::class, 'parseInvoice']);
    Route::get('recalcrevenue', [OrderController::class, 'recalculateRevenue']);
    Route::get('picklistregen', [OrderController::class, 'regenPicklist']);
    Route::get('parsecomlog/{comlog}', [OrderController::class, 'parseComlog']);
    Route::get('ordersofinterest', [OrderController::class, 'ordersOfInterest']);
    Route::get('woocommerce', [OrderController::class, 'parseWoocommerce']);
    Route::get('bustotaal', [OrderController::class, 'wooOrders'])->name('orders.woo');
    Route::get('livewire/order-woo-table/get-orders', [App\Livewire\WooOrdersTable::class, 'getOrders'])->name('livewire.order-woo-table.get-orders');

    //------------------PURCHASE ORDERS---------------//
    Route::get('purchase', [PurchaseOrderController::class, 'index'])->name('purchase-order.index');
    Route::get('livewire/purchase-order-table/get-purchase-orders', [App\Livewire\PurchaseOrderTable::class, 'getPurchaseOrders'])->name('livewire.purchase-order-table.get-purchase-orders');
    Route::get('purchaseorder/new', [PurchaseOrderController::class, 'new'])->name('purchase-order.new');
    Route::get('purchaseorder/edit/{purchaseOrder}', [PurchaseOrderController::class, 'edit'])->name('purchase-order.edit');

    //------------------QUOTES---------------//
    Route::get('quote', [QuoteController::class, 'index'])->name('quote.index');
    Route::get('livewire/quotes-table/get-quotes', [App\Livewire\QuotesTable::class, 'getQuotes'])->name('livewire.quotes-table.get-quotes');
    Route::get('quote/new', [QuoteController::class, 'newQuote'])->name('quote.new');
    Route::get('quote/edit/{quote}', [QuoteController::class, 'editQuote'])->name('quote.edit');

    //------------------SETTINGS---------------//
    Route::get('settings', [SettingsController::class, 'index'])->name('settings.index');
    Route::get('transmissioncost', [TransmissionCostController::class, 'index'])->name('transmissioncost.index');

    //------------------PICKLIST---------------//
    Route::get('picklist', [PicklistController::class, 'index'])->name('picklist.index');

    //------------------TRANSUS---------------//
    Route::get('transus', [TransusController::class, 'index'])->name('transus.index');
    Route::get('transus/upload', [TransusController::class, 'upload'])->name('transus.upload');
    Route::get('livewire/transus-table/get-imports', [\App\Livewire\TransusTable::class, 'getImports'])->name('livewire.transus-table.get-imports');
    Route::get('transus/view/{transusId}', [TransusController::class, 'viewMessage'])->name('transus.view');

    //------------------RAPPORTAGE------------//
    Route::get('rapport', [RapportController::class, 'index'])->name('rapport.index');

    Route::get('/tokens/create', function (Request $request) {
        $token = $request->user()->createToken('pelfin-token');

        return ['token' => $token->plainTextToken];
    });
});
