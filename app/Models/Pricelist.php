<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Pricelist extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function priceTiers()
    {
        return $this->hasMany(CustomerPrice::class);
    }

    public function customers()
    {
        return $this->belongsToMany(Customer::class, 'customer_pricelist');
    }
}
