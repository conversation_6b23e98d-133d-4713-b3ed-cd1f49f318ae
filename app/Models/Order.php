<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class Order extends Model
{
    use HasFactory, LogsActivity;

    protected $guarded = [];

    protected $casts = [
        'delivery_date' => 'datetime',
    ];

    public const STATUS_PROCESSING = 'PROCESSING';
    public const STATUS_SHIPPED = 'SHIPPED';
    public const STATUS_READY_FOR_SHIPPING = 'READY_FOR_SHIPPING';
    public const STATUS_COMPLETE = 'COMPLETE';

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logUnguarded();
    }

    /**
     * @return HasMany
     */
    public function orderitems()
    {
        return $this->hasMany(OrderItem::class);
    }

    /**
     * @return HasOne
     */
    public function address()
    {
        return $this->hasOne(Address::class, 'id', 'address_id');
    }

    /**
     * @return BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * @return BelongsTo
     */
    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * @return HasMany
     */
    public function communication()
    {
        return $this->hasMany(OrderCommunication::class);
    }

    public function comments()
    {
        return $this->hasMany(OrderComment::class);
    }

    public function shipment()
    {
        return $this->hasOne(Shipment::class);
    }

    public function transus()
    {
        return $this->hasOne(Transus::class);
    }

    public function getHasShipmentAttribute()
    {
        return $this->shipment()->exists();
    }

    public function orderLog()
    {
        return $this->hasOne(OrderLog::class);
    }

    public function credit()
    {
        return $this->hasOne(CreditOrder::class);
    }

    public function invoice()
    {
        return $this->hasOne(Invoice::class);
    }

    public function getHasInvoiceAttribute()
    {
        $hasInvoice = InvoiceLink::where('order_id', $this->id)->first();
        if ($hasInvoice) {
            return true;
        }
        return $this->invoice()->exists();
    }
}
