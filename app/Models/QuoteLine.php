<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class QuoteLine extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function quote()
    {
        return $this->belongsTo(Quote::class);
    }

    /**
     * Get the related product, if any.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo|null
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }
}
