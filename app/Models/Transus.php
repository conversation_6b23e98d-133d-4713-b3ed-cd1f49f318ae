<?php

namespace App\Models;

use App\Notifications\SlackNotification;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use SimpleXMLElement;

class Transus extends Model
{
    use HasFactory;

    protected $guarded = [];

    public $parseLog;
    public $selectedAddress;

    public function parseTransusMessage()
    {
        $response = Http::withHeaders([
            'Content-Type' => 'text/xml; charset=UTF-8',
            'SOAPAction' => 'https://webconnect.transus.com/M10110',  // Replace with your SOAPAction
        ])->send('POST', 'https://webconnect.transus.com/exchange.asmx', [
            'body' => '<soap12:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns:xsd="http://www.w3.org/2001/XMLSchema"
        xmlns:soap12="http://www.w3.org/2003/05/soap-envelope">
        <soap12:Body>
            <M10110 xmlns="https://webconnect.transus.com/">
                <ClientID>10809982</ClientID>
                <ClientKey>F602R8IICGPL</ClientKey>
            </M10110>
        </soap12:Body>
    </soap12:Envelope>',
        ]);
//        dd($response->body());
        $this->message = $response->body();
        $this->direction = 'nvt';
        $this->parsed = false;
        $this->save();
        $this->parseXml();
    }

    public function parseXml()
    {
        $tmpxml = simplexml_load_string($this->message, "SimpleXMLElement", LIBXML_NOCDATA);
        $namespaces = $tmpxml->getNamespaces(true);

// Navigate to the Message node
        $messageNode = $tmpxml->children($namespaces['soap'])->Body->children()->M10110Response->Message;
        $transactionId = $tmpxml->children($namespaces['soap'])->Body->children()->M10110Response->TransactionID;
        $this->sendTransusReceiveReceipt($transactionId);
        try {
            $xml = simplexml_load_string(base64_decode($messageNode));
        } catch (Exception $exception) {
            $this->delete();
            return;
        }
        $this->message = base64_decode($messageNode);
        $this->save();
        if(empty($xml->Message)) {
            $this->delete();

            return;
        }

        $this->transus_id = $transactionId;

        $this->parseLog = '<ul  class="list-disc">';

        $existingOrder = Order::where('reference', $xml->Message->OrderNumberBuyer)->first();
        if ($existingOrder) {
            $this->error = 'Order bestaat al met referentie: ' . $xml->Message->OrderNumberBuyer;
            $this->save();
            Notification::route('slack', "*******************************************************************************")
                ->notify(new SlackNotification('Transus bericht dubbel met ID: ' . $this->transus_id));
            return;
        }

        try {
            $order = new Order();
            $customerGln = $xml->Message->InvoiceeGLN ? $xml->Message->InvoiceeGLN : $xml->Message->BuyerGLN;
            $customer = Customer::whereRaw("FIND_IN_SET(?, gln) > 0", [$customerGln])->first();

            if (!$customer) {
                throw new Exception('Klant niet gevonden');
            }
            $this->parseLog .= '<li>Klant gevonden</li>';
            $addressId = $this->searchOrCreateAddress($xml, $customer);
            $order->customer_id = $customer->id;
            $order->user_id = 1;
            if (strlen($xml->Message->RequestedDeliveryDate) > 8) {
                $xmlDeliveryDate = substr($xml->Message->RequestedDeliveryDate, 0, 8);
            } else {
                $xmlDeliveryDate = $xml->Message->RequestedDeliveryDate;
            }
            $deliveryDate = Carbon::createFromFormat('Ymd', $xmlDeliveryDate)->format('Y-m-d');
            $order->delivery_date = $deliveryDate;
            if (strlen($xml->Message->OrderDate) > 8) {
                $xmlOrderDate = substr($xml->Message->OrderDate, 0, 8);
            } else {
                $xmlOrderDate = $xml->Message->OrderDate;
            }
            $orderDate = Carbon::createFromFormat('Ymd', $xmlOrderDate)->format('Y-m-d');
            $order->order_date = $orderDate;
            $order->address_id = $addressId;
            $order->status = 'PROCESSING';
            $order->save();
            $this->parseLog .= '<li>Lege bestelling aangemaakt</li>';
            $cost = 0.00;
            $orderProfitCost = 0.00;
            $total = 0.00;
            $transportLength = 0.00;
            $totalQty = 0;
            $orderLinePrice = 0.00;
            $transportUnitQty = 0;
            $freeShipping = false;
            $orderLog = '';
            $stockPlaces = [];
            $orderLineCount = 1;
            foreach ($xml->Message->Article as $article) {
                try {
                    $product = Product::where('gtin', $article->GTIN)->firstOrFail();

                    if ($product->transport_qty) {
                        if ((int) $article->OrderedQuantity % (int) $product->transport_qty !== 0) {
                            $this->error = 'Aantal gevraagd (' . $article->OrderedQuantity . ') is niet deelbaar door transport eenheid (' . $product->transport_qty . ').';
                            $this->save();
                            $order->delete();

                            return;
                        }
                    }
                } catch (ModelNotFoundException $modelNotFoundException) {
                    $this->error = 'Product niet gevonden met GTIN: ' . $article->GTIN;
                    $this->save();
                    $order->delete();
                    Notification::route('slack', "*******************************************************************************")
                        ->notify(new SlackNotification('Transus bericht fout met ID: ' . $this->transus_id . '. Foutmelding: ' . $this->error));
                    return;
                }

                // Is it a DropShipment or a Stock order
                $priceType = 'S';

                if ($xml->Message->IsDropShipment) {
                    $priceType = 'D';
                }

                $priceList = $customer->load([
                    'pricelists' => function ($query) {
                        $today = now()->toDateString();
                        $query->whereDate('from', '<=', $today)
                            ->whereDate('to', '>=', $today);
                    },
                    'pricelists.priceTiers' => function ($query) use ($product, $article, $priceType) {
                        $query->where('product_id', $product->id)
                            ->where('from_qty', '<=', $article->OrderedQuantity)
                            ->where('to_qty', '>=', $article->OrderedQuantity)
                            ->where('type', $priceType);
                    }
                ]);

                $priceList = $priceList->pricelists->first();
                $price = 0.00;
                $calculation = '';
                if ($priceList) {
                    $priceTier = $priceList->priceTiers->first();
                    if ($priceTier) {
                        $price = $priceTier->price;
                        $calculation = 'Toegepaste prijsstaffel: Van '.$priceTier->from_qty.' tot '.$priceTier->to_qty.': €'.$priceTier->price.' Vanuit Prijslijst: '.$priceList->name;
                        $orderLog .= '<li>Prijsstaffel gevonden: ' . $priceTier->from_qty.' tot '.$priceTier->to_qty.': €'.$priceTier->price.' Vanuit Prijslijst: '.$priceList->name . '</li>';
                        if ($priceTier->free_shipping) {
                            $freeShipping = true;
                            $orderLog .= '<li>Gratis verzending</li>';
                        }
                    } else {
                        $price = $price = $product->price;
                        $orderLog .= '<li>Geen prijsstaffel gevonden. Rekenen met standaard productprijs van €' . $price . '</li>';
                        $calculation = 'Geen prijsstaffel voor dit aantal bij dit product.';
                    }
                } else {
                    // Is there a price on product level for this specific customer?
                    $price = $price = $product->price;
                    $orderLog .= '<li>Geen prijsstaffel gevonden. Rekenen met standaard productprijs van €' . $price . '</li>';
                    $calculation = 'Geen prijsstaffel voor dit aantal bij dit product.';
                }



                $orderLineCost = 0.00;
                $orderLineProfitCost = 0.00;

                $storageProduct = $product->baseStockProducts()->first();

                if ($storageProduct) {
                    $neededStock = $article->OrderedQuantity * $storageProduct->pivot->quantity;

                    $totalStock = 0;

                    // Calculate total available stock
                    foreach ($storageProduct->stock as $stockPlace) {
                        $totalStock += $stockPlace->quantity;
                    }

                    // Check if total stock is sufficient
                    if ($totalStock >= $neededStock) {

                        // Deduct stock from each stock place
                        foreach ($storageProduct->stock as $stockPlace) {
                            if ($neededStock == 0) {
                                break;
                            }

                            $takeStock = min($stockPlace->quantity, $neededStock);

                            $neededStock -= $takeStock;
                            $orderLineProfitCost += ($stockPlace->price * $takeStock);
                            $orderLog .= '<li>Winst/verlies berekening regel ' . $orderLineCount . ': ' . $storageProduct->name . ' -- ' . $takeStock . ' X &euro;' . $stockPlace->price . '</li>';
                            $orderLineCost += $stockPlace->price * $takeStock;

                            $orderLineCost += $stockPlace->transport_cost * $takeStock;
                            $orderLog .= '<li>Transportkosten inkoop regel ' . $orderLineCount . ': ' . $takeStock . ' X &euro;' . $stockPlace->transport_cost . '</li>';
                            // Minus stockplace and add to array for saving after order is created successfully.
                            $stockPlace->quantity -= $takeStock;
                            $stockPlaces[] = $stockPlace;
                        }
                    } else {
                        $orderLog .= '<li>Geen voorraad bij basisproduct. Standaard prijs van basisproduct als kostprijs.</li>';
                        $orderLineCost += $storageProduct->price * $neededStock;
                        $orderLog .= '<li>Berekening met standaard prijs van basisproduct. ' . $neededStock . ' X &euro;' . $storageProduct->price . '</li>';
//                        throw new Exception('Niet genoeg voorraad voor ' . $storageProduct->name);
                    }
                } else {
//                    throw new Exception('Product heeft geen voorraad');
                }

                // END COST AND STOCK

                $profitCostProductCost = 0.00;
                if (count($product->baseProducts) > 0) {
                    foreach ($product->baseProducts as $baseProduct) {
                        if (!$baseProduct->pivot->revenue) {
                            $calcQuantity = $article->OrderedQuantity / $baseProduct->pivot->per_qty;
                            $productCost = $baseProduct->price * $calcQuantity;
                            $orderLineCost += $productCost;
                            $orderLog .= '<li>Korstprijs berekening regel ' . $orderLineCount . ': ' . $baseProduct->name . ' -- ' . $calcQuantity . ' X &euro;' . $baseProduct->price . '</li>';
                        }
                    }
                } else {
                    $orderLog .= '<li>Oude kostprijs berekening regel ' . $orderLineCount . ': ' . $article->OrderedQuantity . ' X €' . $product->cost . '</li>';
                    $orderLineCost = $product->cost * $article->OrderedQuantity;
                }

                $order->orderitems()->create([
                    'product_id' => $product->id,
                    'sku' => $product->sku,
                    'qty' => $article->OrderedQuantity,
                    'product_name' => $product->name,
                    'product_desc' => $product->description,
                    'cost' => $orderLineCost,
                    'profit_cost' => $profitCostProductCost * $article->OrderedQuantity,
                    'price_ex_vat' => $article->OrderedQuantity * $price,
                    'calculation' => $calculation,
                    'order' => $orderLineCount
                ]);
                $cost += $orderLineCost;
                $orderProfitCost += $profitCostProductCost * $article->OrderedQuantity;
                $orderLinePrice += $article->OrderedQuantity * $price;
                $transportUnitLength = $product->transportUnit[$product->transmissionData->unit_type];
                if ($product->transport_qty) {
                    $transportUnits = $article->OrderedQuantity / $product->transport_qty;
                    $transportLength += $transportUnits * $transportUnitLength;
                    $transportUnitQty += $transportUnits;
                } else {
                    $transportLength += $transportUnitLength * $article->OrderedQuantity;
                    $transportUnitQty += $article->OrderedQuantity;
                }

                $totalQty += $article->OrderedQuantity;
                $orderLineCount++;
            }

            $orderLog .= '<li>Transportmeters volledige bestelling: ' . $transportLength . '</li>';

            $transmissionCost = TransmissionCost::where('trailer_length', '>=', $transportLength)
                ->orderBy('trailer_length', 'asc')
                ->first();

            if(!$transmissionCost) {
                $transmissionCost = TransmissionCost::orderBy('id', 'desc')->first();
                $orderLog .= '<li>Geen staffel voor transport lengte ' . $transportLength . '. Gebruikte staffel: ' . $transmissionCost->trailer_length . '</li>';
//                throw new Exception('Geen staffel voor transport lengte ' . $transportLength);
            }

            $transportCost = $transmissionCost->getPriceForPostcode($this->selectedAddress->postal_code, $this->selectedAddress->country);

            $transport = 0.00;
            if (!$freeShipping) {
                $transportPriceTier = $customer->getTransportPriceTier($transportUnitQty);
                $transportPriceTierPrice = $transportPriceTier->getPriceForPostcode($this->selectedAddress->postal_code, $this->selectedAddress->country);
                $transportPriceTierQty = $transportPriceTier->trailer_spot;
                $transport = $transportPriceTierPrice * $transportUnitQty;
            }

            $shipper = 'transmission';
            $order->reference = $xml->Message->OrderNumberBuyer;
            $order->shipper = $shipper;
            $order->transport_cost = $transportCost;
            $order->transport_price = $transport;
            $order->cost = $cost;
            $order->total_ex_vat = $orderLinePrice + $transport;
            $totalExCost = $cost + $transportCost;
            $order->total_cost = $totalExCost;
            $order->profit_cost = $orderProfitCost;
            $vat = 0.00;
            $order->vat = $vat;
            $order->total_incl_vat = $orderLinePrice + $vat;
            $order->revenue = $order->total_ex_vat - $order->total_cost;
            $order->subtotal = $orderLinePrice;

            $order->save();

            foreach ($stockPlaces as $stockPlace) {
                $stockPlace->save();
            }

            $order->orderLog()->create([
                'log' => $orderLog
            ]);

            $this->parseLog .= '<li>Bestelling opgeslagen</li>';

        } catch(Exception $e) {
            $order->delete();
            $this->error = $e->getMessage();
            $this->parseLog .= '<li>Fout bij het verwerken van Transus bericht. Bestelling niet aangemaakt.</li>';
            $this->parseLog .= '</ul>';
            $this->log = $this->parseLog;
            $this->save();
            Notification::route('slack', "*******************************************************************************")
                ->notify(new SlackNotification('Transus bericht fout met ID: ' . $this->transus_id . '. Foutmelding: ' . $this->error));
            return;
        }

        $this->order_id = $order->id;
        $this->parsed = 1;
        $this->error = '';
        $this->parseLog .= '</ul>';
        $this->log = $this->parseLog;


        $this->save();
        Notification::route('slack', "*******************************************************************************")
            ->notify(new SlackNotification('Transus bericht verwerkt met ID: ' . $this->transus_id . '. Bestelling aangemaakt: ' . $this->order_id));
    }

    public function reParseXml()
    {
        $xml = simplexml_load_string($this->message);

        if(empty($xml->Message)) {
            $this->delete();

            return;
        }

        $this->parseLog = '<ul  class="list-disc">';

        try {
            $order = new Order();
            $customerGln = $xml->Message->InvoiceeGLN ? $xml->Message->InvoiceeGLN : $xml->Message->BuyerGLN;
            $customer = Customer::whereRaw("FIND_IN_SET(?, gln) > 0", [$customerGln])->first();

            if (!$customer) {
                throw new Exception('Klant niet gevonden');
            }
            $this->parseLog .= '<li>Klant gevonden</li>';
            $addressId = $this->searchOrCreateAddress($xml, $customer);
            $order->customer_id = $customer->id;
            $order->user_id = 1;
            if (strlen($xml->Message->RequestedDeliveryDate) > 8) {
                $xmlDeliveryDate = substr($xml->Message->RequestedDeliveryDate, 0, 8);
            } else {
                $xmlDeliveryDate = $xml->Message->RequestedDeliveryDate;
            }
            $deliveryDate = Carbon::createFromFormat('Ymd', $xmlDeliveryDate)->format('Y-m-d');
            $order->delivery_date = $deliveryDate;
            if (strlen($xml->Message->OrderDate) > 8) {
                $xmlOrderDate = substr($xml->Message->OrderDate, 0, 8);
            } else {
                $xmlOrderDate = $xml->Message->OrderDate;
            }
            $orderDate = Carbon::createFromFormat('Ymd', $xmlOrderDate)->format('Y-m-d');
            $order->order_date = $orderDate;
            $order->address_id = $addressId;
            $order->status = 'PROCESSING';
            $order->save();
            $this->parseLog .= '<li>Lege bestelling aangemaakt</li>';
            $cost = 0.00;
            $orderProfitCost = 0.00;
            $total = 0.00;
            $transportLength = 0.00;
            $totalQty = 0;
            $orderLinePrice = 0.00;
            $transportUnitQty = 0;
            $freeShipping = false;
            $orderLog = '';

            $stockPlaces = [];
            $orderLineCount = 1;
            foreach ($xml->Message->Article as $article) {
                try {
                    $product = Product::where('gtin', $article->GTIN)->firstOrFail();

                    if ($product->transport_qty) {
                        if ((int) $article->OrderedQuantity % (int) $product->transport_qty !== 0) {
                            $this->error = 'Aantal gevraagd (' . $article->OrderedQuantity . ') is niet deelbaar door transport eenheid (' . $product->transport_qty . ').';
                            $this->save();
                            $order->delete();

                            return;
                        }
                    }
                } catch (ModelNotFoundException $modelNotFoundException) {
                    $this->error = 'Product niet gevonden met GTIN: ' . $article->GTIN;
                    $this->save();
                    $order->delete();
                    Notification::route('slack', "*******************************************************************************")
                        ->notify(new SlackNotification('Transus bericht fout met ID: ' . $this->transus_id . '. Foutmelding: ' . $this->error));
                    return;
                }

                // Is it a DropShipment or a Stock order
                $priceType = 'S';

//                if ($customer->exact_id == 805986) {
//
//                }

                if ($xml->Message->IsDropShipment) {
                    $priceType = 'D';
                }

                $priceList = $customer->load([
                    'pricelists' => function ($query) {
                        $today = now()->toDateString();
                        $query->whereDate('from', '<=', $today)
                            ->whereDate('to', '>=', $today);
                    },
                    'pricelists.priceTiers' => function ($query) use ($product, $article, $priceType) {
                        $query->where('product_id', $product->id)
                            ->where('from_qty', '<=', $article->OrderedQuantity)
                            ->where('to_qty', '>=', $article->OrderedQuantity)
                            ->where('type', $priceType);
                    }
                ]);

                $priceList = $priceList->pricelists->first();
                $price = 0.00;
                $calculation = '';
                if ($priceList) {
                    $priceTier = $priceList->priceTiers->first();
                    if ($priceTier) {
                        $price = $priceTier->price;
                        $calculation = 'Toegepaste prijsstaffel: Van '.$priceTier->from_qty.' tot '.$priceTier->to_qty.': €'.$priceTier->price.' Vanuit Prijslijst: '.$priceList->name;
                        $orderLog .= '<li>Prijsstaffel gevonden: ' . $priceTier->from_qty.' tot '.$priceTier->to_qty.': €'.$priceTier->price.' Vanuit Prijslijst: '.$priceList->name . '</li>';
                        if ($priceTier->free_shipping) {
                            $freeShipping = true;
                            $orderLog .= '<li>Gratis verzending</li>';
                        }
                    } else {
                        $price = $price = $product->price;
                        $orderLog .= '<li>Geen prijsstaffel gevonden. Rekenen met standaard productprijs van €' . $price . '</li>';
                        $calculation = 'Geen prijsstaffel voor dit aantal bij dit product.';
                    }
                } else {
                    // Is there a price on product level for this specific customer?
                    $price = $price = $product->price;
                    $orderLog .= '<li>Geen prijsstaffel gevonden. Rekenen met standaard productprijs van €' . $price . '</li>';
                    $calculation = 'Geen prijsstaffel voor dit aantal bij dit product.';
                }



                // COST AND STOCK
                $orderLineCost = 0.00;
                $orderLineProfitCost = 0.00;

                $storageProduct = $product->baseStockProducts()->first();

                if ($storageProduct) {
                    $neededStock = $article->OrderedQuantity * $storageProduct->pivot->quantity;

                    $totalStock = 0;

                    // Calculate total available stock
                    foreach ($storageProduct->stock as $stockPlace) {
                        $totalStock += $stockPlace->quantity;
                    }

                    // Check if total stock is sufficient
                    if ($totalStock >= $neededStock) {

                        // Deduct stock from each stock place
                        foreach ($storageProduct->stock as $stockPlace) {
                            if ($neededStock == 0) {
                                break;
                            }

                            $takeStock = min($stockPlace->quantity, $neededStock);

                            $neededStock -= $takeStock;
                            $orderLineProfitCost += ($stockPlace->price * $takeStock);
                            $orderLog .= '<li>Winst/verlies berekening regel ' . $orderLineCount . ': ' . $storageProduct->name . ' -- ' . $takeStock . ' X &euro;' . $stockPlace->price . '</li>';
                            $orderLineCost += $stockPlace->price * $takeStock;

                            $orderLineCost += $stockPlace->transport_cost * $takeStock;
                            $orderLog .= '<li>Transportkosten inkoop regel ' . $orderLineCount . ': ' . $takeStock . ' X &euro;' . $stockPlace->transport_cost . '</li>';
                            // Minus stockplace and add to array for saving after order is created successfully.
                            $stockPlace->quantity -= $takeStock;
                            $stockPlaces[] = $stockPlace;
                        }
                    } else {
                        $orderLog .= '<li>Geen voorraad bij basisproduct. Standaard prijs van basisproduct als kostprijs.</li>';
                        $orderLineCost += $storageProduct->price * $neededStock;
                        $orderLog .= '<li>Berekening met standaard prijs van basisproduct. ' . $neededStock . ' X &euro;' . $storageProduct->price . '</li>';
//                        throw new Exception('Niet genoeg voorraad voor ' . $storageProduct->name);
                    }
                } else {

//                    throw new Exception('Product heeft geen voorraad');
                }

                $profitCostProductCost = 0.00;
                if (count($product->baseProducts) > 0) {
                    foreach ($product->baseProducts as $baseProduct) {
                        if (!$baseProduct->pivot->revenue) {
                            $calcQuantity = $article->OrderedQuantity / $baseProduct->pivot->per_qty;
                            $productCost = $baseProduct->price * $calcQuantity;
                            $orderLineCost += $productCost;
                            $orderLog .= '<li>Korstprijs berekening regel ' . $orderLineCount . ': ' . $baseProduct->name . ' -- ' . $calcQuantity . ' X &euro;' . $baseProduct->price . '</li>';
                        }
                    }
                } else {
                    $orderLog .= '<li>Oude kostprijs berekening regel ' . $orderLineCount . ': ' . $article->OrderedQuantity . ' X €' . $product->cost . '</li>';
                    $orderLineCost = $product->cost * $article->OrderedQuantity;
                }

                $order->orderitems()->create([
                    'product_id' => $product->id,
                    'sku' => $product->sku,
                    'qty' => $article->OrderedQuantity,
                    'product_name' => $product->name,
                    'product_desc' => $product->description,
                    'cost' => $orderLineCost,
                    'profit_cost' => $orderLineProfitCost,
                    'price_ex_vat' => $article->OrderedQuantity * $price,
                    'calculation' => $calculation,
                    'order' => $orderLineCount
                ]);
                $cost += $orderLineCost;
                $orderProfitCost += $profitCostProductCost * $article->OrderedQuantity;
                $orderLinePrice += $article->OrderedQuantity * $price;
                $transportUnitLength = $product->transportUnit[$product->transmissionData->unit_type];
                if ($product->transport_qty) {
                    $transportUnits = $article->OrderedQuantity / $product->transport_qty;
                    $transportLength += $transportUnits * $transportUnitLength;
                    $transportUnitQty += $transportUnits;
                } else {
                    $transportLength += $transportUnitLength * $article->OrderedQuantity;
                    $transportUnitQty += $article->OrderedQuantity;
                }

                $totalQty += $article->OrderedQuantity;
                $orderLineCount++;
            }

            $orderLog .= '<li>Transportmeters volledige bestelling: ' . $transportLength . '</li>';

            $transmissionCost = TransmissionCost::where('trailer_length', '>=', $transportLength)
                ->orderBy('trailer_length', 'asc')
                ->first();
            if(!$transmissionCost) {
                $transmissionCost = TransmissionCost::orderBy('id', 'desc')->first();
                $orderLog .= '<li>Geen staffel voor transport lengte ' . $transportLength . '. Gebruikte staffel: ' . $transmissionCost->trailer_length . '</li>';
//                throw new Exception('Geen staffel voor transport lengte ' . $transportLength);
            }
            $transportCost = $transmissionCost->getPriceForPostcode($this->selectedAddress->postal_code, $this->selectedAddress->country);

            $transport = 0.00;
            if (!$freeShipping) {
                $transportPriceTier = $customer->getTransportPriceTier($transportUnitQty);
                $transportPriceTierPrice = $transportPriceTier->getPriceForPostcode($this->selectedAddress->postal_code, $this->selectedAddress->country);
                $transportPriceTierQty = $transportPriceTier->trailer_spot;
                $transport = $transportPriceTierPrice * $transportUnitQty;
            }

            $shipper = 'transmission';
            $order->reference = $xml->Message->OrderNumberBuyer;
            $order->shipper = $shipper;
            $order->transport_cost = $transportCost;
            $order->transport_price = $transport;
            $order->cost = $cost;
            $order->total_ex_vat = $orderLinePrice + $transport;
            $totalExCost = $cost + $transportCost;
            $order->total_cost = $totalExCost;
            $order->profit_cost = $orderProfitCost;
            $vat = 0.00;
            $order->vat = $vat;
            $order->total_incl_vat = $orderLinePrice + $vat;
            $order->revenue = $order->total_ex_vat - $order->total_cost;
            $order->subtotal = $orderLinePrice;

            $order->save();

            foreach ($stockPlaces as $stockPlace) {
                $stockPlace->save();
            }

            $order->orderLog()->create([
                'log' => $orderLog
            ]);

            $this->parseLog .= '<li>Bestelling opgeslagen</li>';

        } catch(Exception $e) {
            $order->delete();
            $this->error = $e->getMessage();
            $this->parseLog .= '<li>Fout bij het verwerken van Transus bericht. Bestelling niet aangemaakt.</li>';
            $this->parseLog .= '</ul>';
            $this->log = $this->parseLog;
            $this->save();

            return;
        }

        $this->order_id = $order->id;
        $this->parsed = 1;
        $this->error = '';
        $this->parseLog .= '</ul>';
        $this->log = $this->parseLog;


        $this->save();
    }

    public function searchOrCreateAddress($xml, $customer)
    {
        if ($xml->Message->UltimateConsignee) {
            $name = $xml->Message->UltimateConsignee;
            $address = $xml->Message->UltimateConsigneeAddress;
            $postalCode = $xml->Message->UltimateConsigneeZipcode;
            $city = $xml->Message->UltimateConsigneeCity;
            $country = $xml->Message->UltimateConsigneeCountry ? $xml->Message->UltimateConsigneeCountry: 'NL';
            $gln = NULL;
        } else {
            $name = $xml->Message->DeliveryParty;
            $address = $xml->Message->DeliveryPartyAddress;
            $postalCode = $xml->Message->DeliveryPartyZipcode;
            $city = $xml->Message->DeliveryPartyCity;
            $country = $xml->Message->DeliveryPartyCountry ? $xml->Message->DeliveryPartyCountry : 'NL';
            $gln = $xml->Message->DeliveryPartyGLN ? $xml->Message->DeliveryPartyGLN : NULL;
        }

        $splitStreetHousenumber = $this->splitStreetAndNumber($address);
//        $existingAddress = Address::where('postal_code', $postalCode)
//            ->where('housenumber', $splitStreetHousenumber['number'])
//            ->first();
//
//        if ($existingAddress) {
//            $this->parseLog .= '<li>Adres gevonden</li>';
//            $this->selectedAddress = $existingAddress;
//            return $existingAddress->id;
//        }

        $email = $xml->Message->DeliveryPartyContactPersonEmail ? $xml->Message->DeliveryPartyContactPersonEmail : NULL;
        $telephone = $xml->Message->DeliveryPartyContactPersonTelephone ? $xml->Message->DeliveryPartyContactPersonTelephone : NULL;

        $newAddress = $customer->address()->create([
            'name' => $name,
            'type' => 'shipping',
            'street' => $splitStreetHousenumber['street'],
            'housenumber' => $splitStreetHousenumber['number'],
            'postal_code' => $postalCode,
            'city' => $city,
            'country' => $country,
            'telephone' => $telephone,
            'email' => $email,
            'gln' => $gln
        ]);
        $this->selectedAddress = $newAddress;
        $this->parseLog .= '<li>Nieuw adres aangemaakt</li>';

        return $newAddress->id;
    }

    private function splitStreetAndNumber($address) {
        if (preg_match('/^(.*?\D)\s*(\d+\s*\w*)$/', $address, $matches)) {
            $street = trim($matches[1]);
            $number = $matches[2];

            return ['street' => $street, 'number' => $number];
        }

        throw new Exception('Address could not be split into street and housenumber');
    }

    protected function sendTransusReceiveReceipt($transactionId)
    {
        $response = Http::withHeaders([
            'Content-Type' => 'text/xml; charset=UTF-8',
            'SOAPAction' => 'https://webconnect.transus.com/M10300',
        ])->send('POST', 'https://webconnect.transus.com/exchange.asmx', [
            'body' => '
                <soap12:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                    xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                    xmlns:soap12="http://www.w3.org/2003/05/soap-envelope">
                    <soap12:Body>
                        <M10300 xmlns="https://webconnect.transus.com/">
                            <ClientID>10809982</ClientID>
                            <ClientKey>F602R8IICGPL</ClientKey>
                            <TransactionID>' . $transactionId . '</TransactionID>
                            <Status>0</Status>
                        </M10300>
                    </soap12:Body>
                </soap12:Envelope>',
        ]);
    }

    public function sendTransusShippedMessage() {
        $transusMessage = TransusMessage::where('sent', 0)->first();
        if ($transusMessage) {
            $encodedMessage = base64_encode($transusMessage->message);
            $response = Http::withHeaders([
                'Content-Type' => 'text/xml; charset=UTF-8',
                'SOAPAction' => 'https://webconnect.transus.com/M10100',
            ])->send('POST', 'https://webconnect.transus.com/exchange.asmx', [
                'body' => '
                    <soap12:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                        xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                        xmlns:soap12="http://www.w3.org/2003/05/soap-envelope">
                        <soap12:Body>
                            <M10100 xmlns="https://webconnect.transus.com/">
                                <ClientID>10809982</ClientID>
                                <ClientKey>F602R8IICGPL</ClientKey>
                                <Message>' . $encodedMessage . '</Message>
                            </M10100>
                        </soap12:Body>
                    </soap12:Envelope>',
            ]);
            $transusMessage->response = $response->body();
            $transusMessage->sent = 1;
            $transusMessage->save();
        }
    }

    public function order()
    {
        return $this->belongsTo(Order::class);
    }
}
