<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StockPlace extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function stockMovements()
    {
        return $this->hasMany(StockMovement::class);
    }

    public function subtractStock($orderedQty, $orderlineId)
    {
        $this->stockMovements()->create([
            'starting_qty' => $this->qty,
            'qty' => $orderedQty,
            'ending_qty' => $this->qty - $orderedQty,
            'orderline_id' => $orderlineId
        ]);
        $this->qty -= $orderedQty;
    }
}
