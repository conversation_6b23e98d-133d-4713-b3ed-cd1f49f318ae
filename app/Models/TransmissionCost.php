<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TransmissionCost extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $casts = [
        'from_date' => 'datetime',
        'to_date' => 'datetime'
    ];

    public function getPriceForPostcode($postalCode, $country)
    {
        $rangesNorth = $this->getRanges('postcodes_north');
        $rangesZV = $this->getRanges('postcodes_zv');
        $rangesWadden = $this->getRanges('postcodes_we');
        $fuelSurchargeSetting = Settings::where('key', 'transmission_fuelsurcharge')->first();
        $fuelSurcharge = $fuelSurchargeSetting->value / 100 + 1;

        if ($country === 'BE') {
            return $this->price_be * $fuelSurcharge;
        }

        foreach($rangesNorth as $range) {
            if($postalCode >= $range['from'] && $postalCode <= $range['to']) {

                return $this->price_north * $fuelSurcharge;
            }
        }

        foreach($rangesZV as $range) {
            if($postalCode >= $range['from'] && $postalCode <= $range['to']) {
                return $this->price_zv * $fuelSurcharge;
            }
        }

        foreach($rangesWadden as $range) {
            if($postalCode >= $range['from'] && $postalCode <= $range['to']) {
                return $this->price_we * $fuelSurcharge;
            }
        }

        return $this->price_nl * $fuelSurcharge;
    }

    /**
     * @return array|\int[][]
     */
    protected function getRanges($key): array
    {
        $rangesString = Settings::where('key', $key)->first();
        $rangesArray = explode(', ', $rangesString->value);

        return array_map(function ($range) {
            $fromTo = explode('-', $range);
            return [
                'from' => (int)$fromTo[0],
                'to' => (int)$fromTo[1]
            ];
        }, $rangesArray);
    }
}
