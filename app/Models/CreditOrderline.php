<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CreditOrderline extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function orderline()
    {
        return $this->belongsTo(OrderItem::class, 'order_item_id', 'id');
    }

    public function creditOrder()
    {
        return $this->belongsTo(CreditOrder::class);
    }
}
