<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TransportPricelistTier extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function transportPricelist()
    {
        return $this->belongsTo(TransportPricelist::class);
    }

    public function getPriceForPostcode($postalCode, $country)
    {
        if ($country === 'BE') {
            return $this->price_be;
        }

        $rangesNorth = $this->getRanges('postcodes_north');
        $rangesZV = $this->getRanges('postcodes_zv');
        $rangesWadden = $this->getRanges('postcodes_we');

        foreach($rangesNorth as $range) {
            if($postalCode >= $range['from'] && $postalCode <= $range['to']) {
                return $this->price_nl;
            }
        }

        foreach($rangesZV as $range) {
            if($postalCode >= $range['from'] && $postalCode <= $range['to']) {
                return $this->price_zv;
            }
        }

        foreach($rangesWadden as $range) {
            if($postalCode >= $range['from'] && $postalCode <= $range['to']) {
                return $this->price_we;
            }
        }

        return $this->price_nl;
    }

    protected function getRanges($key): array
    {
        $rangesString = Settings::where('key', $key)->first();
        $rangesArray = explode(', ', $rangesString->value);

        return array_map(function ($range) {
            $fromTo = explode('-', $range);
            return [
                'from' => (int)$fromTo[0],
                'to' => (int)$fromTo[1]
            ];
        }, $rangesArray);
    }
}
