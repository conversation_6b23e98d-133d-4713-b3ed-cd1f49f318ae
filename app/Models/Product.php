<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Product extends Model
{
    use HasFactory;

    protected $guarded = [];

    public $transportUnit = [
      'EP' => 0.40,
      'BP' => 0.50
    ];

    public function transmissionData()
    {
        return $this->hasOne(TransmissionProductData::class);
    }

    public function customerPrices()
    {
        return $this->hasMany(CustomerPrice::class);
    }

    public function gs1Data()
    {
        return $this->hasOne(Gs1Data::class);
    }

    public function storageProduct()
    {
        return $this->belongsTo(Product::class, 'storage_product');
    }

    public function associatedProducts()
    {
        return $this->hasMany(Product::class, 'storage_product');
    }

    public function stockPlaces()
    {
        return $this->hasMany(StockPlace::class)->orderBy('position');
    }

    public function baseProducts()
    {
        return $this->belongsToMany(BaseProduct::class)->withPivot('quantity', 'revenue', 'per_qty', 'stock')->withTimestamps();
    }

    public function baseStockProducts()
    {
        return $this->belongsToMany(BaseProduct::class)
            ->withPivot('quantity', 'revenue', 'per_qty', 'stock')
            ->withTimestamps()
            ->wherePivot('stock', 1);
    }

    public function skus()
    {
        return $this->hasMany(SKU::class);
    }
}
