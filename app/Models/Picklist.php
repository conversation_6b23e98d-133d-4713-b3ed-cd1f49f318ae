<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Picklist extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function orderitem()
    {
        return $this->belongsTo(OrderItem::class, 'order_item_id', 'id');
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }
}
