<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Customer extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function getGlnAttribute($value)
    {
        return explode(',', $value);
    }

    public function setGlnAttribute($value)
    {
        if (is_array($value)) {
            $this->attributes['gln'] = implode(',', $value);
        } else {
            $this->attributes['gln'] = $value;
        }
    }

    public function transportPriceAgreement()
    {
        return $this->hasOne(TransportPriceAgreement::class);
    }

    public function getApplicableTransportPriceAgreement()
    {
        // Check if the customer has a custom agreement
        if ($this->transportPriceAgreement !== null) {
            return $this->transportPriceAgreement;
        }

        // Return the base agreement if the customer does not have a custom one
        return TransportPriceAgreement::where('is_base', true)->first();
    }

    public function interactions()
    {
        return $this->hasMany(Interaction::class);
    }

    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    public function productPrices()
    {
        return $this->hasMany(CustomerPrice::class);
    }

    public function getTransportPriceTier($quantity)
    {
        if ($quantity == 0) {
            $quantity = 1;
        }

        $currentDate = Carbon::now();
        $priceAgreement = $this->transportpricelists()
            ->whereDate('from', '<=', $currentDate)
            ->whereDate('to', '>=', $currentDate)
            ->first();
        if (!$priceAgreement) {
            $priceAgreement = TransportPricelist::where('name', 'Standaard')->first();
        }

        $priceTier = $priceAgreement->transportPriceTiers()
            ->where('trailer_spot', '<=', $quantity)
            ->orderBy('trailer_spot', 'desc')
            ->first();

        return $priceTier;
    }

    public function quotes()
    {
        return $this->hasMany(Quote::class);
    }

    public function address()
    {
        return $this->hasMany(Address::class);
    }

    public function pricelists()
    {
        return $this->belongsToMany(Pricelist::class, 'customer_pricelist');
    }

    public function transportpricelists()
    {
        return $this->belongsToMany(TransportPricelist::class, 'customer_transport_pricelist');
    }
}
