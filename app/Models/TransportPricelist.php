<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TransportPricelist extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function transportPriceTiers()
    {
        return $this->hasMany(TransportPricelistTier::class);
    }

    public function customers()
    {
        return $this->belongsToMany(Customer::class, 'customer_transport_pricelist');
    }
}
