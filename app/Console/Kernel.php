<?php

namespace App\Console;

use App\Http\Controllers\OrderController;
use App\Models\Transus;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        $schedule->call(function() {
            $transus = new Transus();
            $transus->parseTransusMessage();
        })->everyFiveMinutes();

        $schedule->call(function() {
            $transus = new Transus();
            $transus->sendTransusShippedMessage();
        })->everyTwoMinutes();

        $schedule->call(function() {
            $orderController = resolve(OrderController::class);
            $orderController->checkShipments();
        })->twiceDaily(9,13);

        $schedule->call(function() {
            $orderController = resolve(OrderController::class);
            $orderController->parseComlogs();
        })->everyFifteenMinutes();

        $schedule->call(function() {
            $orderController = resolve(OrderController::class);
            $orderController->parseWoocommerce();
        })->dailyAt(2);
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
