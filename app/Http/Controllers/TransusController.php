<?php

namespace App\Http\Controllers;

use App\Models\Transus;
use Illuminate\Http\Request;

class TransusController extends Controller
{
    public function index()
    {
        return view('transus');
    }

    public function viewMessage($transusId)
    {
        return view('transus-view')->with('transusId', $transusId);
    }

    public function upload()
    {
        return view('transus-upload');
    }
}
