<?php

namespace App\Http\Controllers;

use App\Models\BaseProduct;
use App\Models\Comlog;
use App\Models\Product;
use App\Models\TransmissionProductData;
use Illuminate\Http\Request;

class ProductController extends Controller
{
    /**
     * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View|\Illuminate\Foundation\Application
     */
    public function index()
    {
        return view('products');
    }

    /**
     * @param Product $product
     * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View|\Illuminate\Foundation\Application
     */
    public function edit(Product $product)
    {
        return view('edit-product')->with('product', $product);
    }

    public function update(Request $request, $id)
    {
        // Validate the input fields
//        $validatedData = $request->validate([
//            'name' => 'required',
//            'description' => 'required',
//            'price' => 'required|numeric',
//            // Include other fields you want to validate
//        ]);

        // Find the product
        $product = Product::findOrFail($id);

        // Update the product attributes
        $product->name = $request->get('name');
        $product->description = $request->get('description');
        $product->gtin = $request->get('gtin');
        $product->transport_qty = $request->get('transport_qty');

        // Check if TransmissionProductData relationship exists
        if ($request->get('weight')) {
            if ($product->transmissionData) {
                // Update the existing TransmissionProductData
                $transmissionProductData = $product->transmissionData;
                $transmissionProductData->transport_weight = $request->get('weight');
                $transmissionProductData->height = $request->get('height');
                $transmissionProductData->length = $request->get('length');
                $transmissionProductData->width = $request->get('width');
                $transmissionProductData->unit_type = $request->get('unit_type');
                $transmissionProductData->save();
            } else {
                // Create a new TransmissionProductData and associate it with the product
                $transmissionProductData = new TransmissionProductData;
                $transmissionProductData->transport_weight = $request->get('weight');
                $transmissionProductData->height = $request->get('height');
                $transmissionProductData->length = $request->get('length');
                $transmissionProductData->width = $request->get('width');
                $transmissionProductData->unit_type = $request->get('unit_type');
                $product->transmissionData()->save($transmissionProductData);
            }
        }

        $product->save();

        return redirect()->route('product.index')->with('success', 'Product updated successfully');
    }

    public function saveProduct(Request $request)
    {
        $comlog = new Comlog();
        $comlog->message = $request->getContent();
        $comlog->save();
    }

    public function apiProducts()
    {
        $products = Product::where('dealer_visible', 1)->get();

        return response()->json($products);
    }

    public function base()
    {
        return view('product-base');
    }

    public function baseProductEdit(BaseProduct $baseProduct)
    {
        return view('product-base-edit')->with('baseProduct', $baseProduct);
    }

    public function newBaseProduct()
    {
        return view('product-base-new');
    }

    public function wooProducts()
    {
        return view('wooproducts');
    }

    public function newProduct()
    {
        return view('product-new');
    }
}
