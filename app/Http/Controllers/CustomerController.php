<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use App\Models\Order;
use App\Models\Product;
use App\Models\TransportPriceAgreement;
use App\Notifications\SlackNotification;
use App\Notifications\Transus;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Notification;

class CustomerController extends Controller
{
    public function index()
    {
//        $filename = public_path('Debitr.csv');
//        $handle = fopen($filename, 'r');
//        $header = null;
//        $data = [];
//        while (($row = fgetcsv($handle, null, ';')) !== false)
//        {
//            if (!$header) {
//                $header = $row;
//            } else {
//                $importData = array_combine($header, $row);
//                $customer = new Customer;
//                $customer->exact_id = $importData['debnr'];
//                $customer->name = $importData['naam'];
//                $customer->address = $importData['adres1'];
//                $customer->postal_code = $importData['postcode'];
//                $customer->city = $importData['woonpl'];
//                $customer->country = $importData['landcode'];
//                $customer->email = $importData['email'];
//                $customer->credit_limit = $importData['kredlimiet'];
//                $customer->save();
//            }
//
//        }
//        fclose($handle);
//        echo "Klaar";

        return view('customers');
    }

    public function edit(Customer $customer)
    {
        $customer->load('transportPriceAgreement.priceTiers', 'transportPriceAgreement.fuelPremium');
        if ($customer->transportPriceAgreement === null) {
            $baseAgreement = TransportPriceAgreement::with('priceTiers', 'fuelPremium')->where('is_base', true)->first();

            $customer->setRelation('transportPriceAgreement', $baseAgreement);
        }

        return view('edit-customer')->with('customer', $customer);
    }

    public function update(Request $request, $id)
    {
        $customer = Customer::find($id);
        $customer->gln = $request->get('gln');
//        $customer->name = $request->get('name');
//        $customer->address = $request->get('address');
//        $customer->postal_code = $request->get('postal_code');
//        $customer->city = $request->get('city');
//        $customer->country = $request->get('country');
//        $customer->email = $request->get('email');
        $customer->save();

        return redirect()->back();
    }

    public function new()
    {
        return view('new-customer');
    }

    public function save(Request $request)
    {
        $customer = Customer::create([
            'exact_id' => $request->get('exact'),
            'name' => $request->get('name'),
            'address' => $request->get('address'),
            'postal_code' => $request->get('postal_code'),
            'city' => $request->get('city'),
            'email' => $request->get('email'),
            'gln' => $request->get('gln'),
            'country' => $request->get('country')
        ]);

        return redirect()->route('customer.index');
    }

    public function prices($customerId)
    {
        $products = Product::with(['customerPrices' => function ($query) use ($customerId) {
            $query->where('customer_id', $customerId);
        }])->get();

        return view('customerprices')->with('products', $products);
    }

    public function orders()
    {
        return view('customer-orders');
    }

    public function customerOrder(Order $order)
    {
        return view('customer-order')->with('order', $order);
    }

    public function newOrder()
    {
        return view('customer-new-order');
    }
}
