<?php

namespace App\Http\Controllers;

use App\Models\Pricelist;
use App\Models\Product;
use App\Models\TransportPricelist;
use Illuminate\Http\Request;

class PricelistController extends Controller
{
    public function index()
    {
        return view('pricelists');
    }

    public function edit(Pricelist $pricelist)
    {
        return view('pricelists-edit')->with('pricelist', $pricelist);
    }

    public function print(Pricelist $pricelist)
    {
        $dropShipCheckboxValues = [];
        $dropShipProductPrices = [];
        $checkboxValues = [];
        $productPrices = [];
        $products = Product::all();
        foreach ($pricelist->priceTiers as $priceTier) {

            if ($priceTier['type'] == 'D') {
                $dropShipCheckboxValues[$priceTier->product_id] = $priceTier->free_shipping;
                $dropShipProductPrices[$priceTier->product_id][$priceTier->id] = [
                    'id' => $priceTier->id,
                    'from_qty' => $priceTier->from_qty,
                    'to_qty' => $priceTier->to_qty,
                    'price' => $priceTier->price
                ];
            } else {
                $checkboxValues[$priceTier->product_id] = $priceTier->free_shipping;
                $productPrices[$priceTier->product_id][$priceTier->id] = [
                    'id' => $priceTier->id,
                    'from_qty' => $priceTier->from_qty,
                    'to_qty' => $priceTier->to_qty,
                    'price' => $priceTier->price
                ];
            }
        }
        $priceAgreement = TransportPricelist::where('name', 'Standaard')->first();
        return view('pricelist-print')->with([
            'pricelist' => $pricelist,
            'dropShipCheckboxValues' => $dropShipCheckboxValues,
            'dropShipProductPrices' => $dropShipProductPrices,
            'checkboxValues' => $checkboxValues,
            'productPrices' => $productPrices,
            'products' => $products,
            'priceAgreement' => $priceAgreement
        ]);
    }
}
