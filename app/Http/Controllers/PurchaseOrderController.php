<?php

namespace App\Http\Controllers;

use App\Models\PurchaseOrder;
use Illuminate\Http\Request;

class PurchaseOrderController extends Controller
{
    public function index()
    {
        return view('purchase-orders');
    }

    public function new()
    {
        return view('purchase-order-new');
    }

    public function edit(PurchaseOrder $purchaseOrder)
    {
        return view('purchase-order-edit')->with('purchaseOrder', $purchaseOrder);
    }
}
