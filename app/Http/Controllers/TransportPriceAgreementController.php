<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use App\Models\FuelPremium;
use App\Models\TransportPriceAgreement;
use App\Models\TransportPriceTier;
use Illuminate\Http\Request;

class TransportPriceAgreementController extends Controller
{
    public function new($customer)
    {
        $baseAgreement = TransportPriceAgreement::with('priceTiers', 'fuelPremium')->where('is_base', true)->first();
        return view('transportagreement-new')->with('baseAgreement', $baseAgreement)->with('customer_id', $customer);
    }

    public function edit(TransportPriceAgreement $transportPriceAgreement)
    {
        $transportPriceAgreement->load('priceTiers');

        return view('transportagreement-edit')->with('transportPriceAgreement', $transportPriceAgreement);
    }

    public function save(Request $request, $customer) {
        $currentCustomer = Customer::find($customer);

        $tiers = $request->get('tier');
        $transportAgreement = new TransportPriceAgreement;
        $currentCustomer->transportPriceAgreement()->save($transportAgreement);
        $priceTiers = [];

        foreach ($tiers as $tier) {
            $priceTier = new TransportPriceTier;
            $priceTier->trailer_spot = $tier['spot'];
            $priceTier->price_nl = str_replace(',', '.', $tier['price_nl']);
            $priceTier->price_be = str_replace(',', '.', $tier['price_be']);
            $priceTier->price_zv = str_replace(',', '.', $tier['price_zv']);
            $priceTier->price_we = str_replace(',', '.', $tier['price_we']);
            $priceTiers[] = $priceTier;
        }
        $transportAgreement->priceTiers()->saveMany($priceTiers);

        return redirect()->route('customer.edit', ['customer' => $currentCustomer->id]);
    }

    public function update(Request $request, $transportPriceAgreementId)
    {
        $tiers = $request->get('tier');
        $newTiers = $request->get('newtier');

        if ($tiers) {
            foreach ($tiers as $tier) {
                $priceTier = TransportPriceTier::find($tier['id']);
                $priceTier->trailer_spot = $tier['spot'];
                $priceTier->price_nl = str_replace(',', '.', $tier['price_nl']);
                $priceTier->price_be = str_replace(',', '.', $tier['price_be']);
                $priceTier->price_zv = str_replace(',', '.', $tier['price_zv']);
                $priceTier->price_we = str_replace(',', '.', $tier['price_we']);
                $priceTier->save();
            }
        }


        $transportPriceAgreement = TransportPriceAgreement::find($transportPriceAgreementId);

        if ($newTiers) {
            $priceTiers = [];
            foreach ($newTiers as $tier) {
                $priceTier = new TransportPriceTier;
                $priceTier->trailer_spot = $tier['spot'];
                $priceTier->price_nl = str_replace(',', '.', $tier['price_nl']);
                $priceTier->price_be = str_replace(',', '.', $tier['price_be']);
                $priceTier->price_zv = str_replace(',', '.', $tier['price_zv']);
                $priceTier->price_we = str_replace(',', '.', $tier['price_we']);
                $priceTiers[] = $priceTier;
            }

            $transportPriceAgreement->priceTiers()->saveMany($priceTiers);
        }

        return redirect()->route('customer.edit', ['customer' => $transportPriceAgreement->customer_id]);
    }

}
