<?php

namespace App\Http\Controllers;

use App\Models\Comlog;
use App\Models\Invoice;
use App\Models\InvoiceLink;
use App\Models\Order;
use App\Models\Picklist;
use App\Models\Product;
use App\Models\Settings;
use App\Models\Transus;
use App\Models\TransusMessage;
use App\Models\Woocommerce;
use App\Notifications\SlackNotification;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Notification;

class OrderController extends Controller
{
    public function index()
    {
        return view('orders');
    }

    public function finishedOrders()
    {
        return view('orders-finished');
    }

    public function unsentOrders()
    {
        return view('orders-unsent');
    }

    public function allOrders()
    {
        return view('orders-all');
    }

    public function wooOrders()
    {
        return view('orders-woo');
    }

    public function new()
    {
        return view('new-order');
    }

    public function edit(Order $order)
    {
        $order->load('customer', 'address', 'orderitems.product');
        return view('edit-order')->with('order', $order);
    }

    public function credit(Order $order)
    {
        $order->load('customer', 'address', 'orderitems.product');
        return view('credit-order')->with('order', $order);
    }

    public function print(Order $order)
    {
        $order->load('customer', 'address');
        return view('order-print')->with('order', $order);
    }

    /**
     * API route api/orders
     * Used by Exact to get orders that are COMPLETED
     *
     * @return \Illuminate\Http\JsonResponse|void
     */
    public function getOrder()
    {
        $order = Order::where('status', 'COMPLETED')->where('exact', 0)->first();
        if ($order) {
            $orders = [];
            $customer = $order->customer;
            $address = $order->address;

            $orderArray = [];
            $orderArray['type'] = 'V';
            $orderArray['order_id'] = $order->id;
            $orderArray['customer_id'] = $customer->exact_id;
            $orderArray['delivery_date'] = str_replace('00:00:00', '', $order->delivery_date);
            if (!$address) {
                $orderArray['delivery_company'] = $customer->name;
                $orderArray['delivery_name'] = $customer->name;
                $orderArray['delivery_street'] = $customer->address;
                $orderArray['delivery_postalcode'] = $customer->postal_code;
                $orderArray['delivery_city'] = $customer->city;
                $orderArray['delivery_country'] = $customer->country;
            } else {
                $orderArray['delivery_company'] = $address->company_name;
                $orderArray['delivery_name'] = $address->name;
                $orderArray['delivery_street'] = $address->street . ' ' . $address->housenumber;
                $orderArray['delivery_postalcode'] = $address->postal_code;
                $orderArray['delivery_city'] = $address->city;
                $orderArray['delivery_country'] = $address->country;
            }
            $orderArray['total'] = $order->total_ex_vat;
            if ($customer->exact_id == 806984) {
                $orderArray['transport_price'] = 0.00;
            } else {
                $orderArray['transport_price'] = $order->transport_price;
            }
            $orderArray['order_number_buyer'] = $order->reference;
            $orderArray['lines'] = [];

            $count = 1;
            foreach($order->orderitems as $orderItem) {
                $orderline = [];
                $orderline['line_number'] = $count;
                if ($orderItem->product->storageProduct) {
                    $orderItemQty = $orderItem->product->storage_product_qty * $orderItem->qty;
                    $orderline['sku'] = $orderItem->product->storageProduct->sku;
                    $orderline['qty'] = $orderItemQty;
                } else {
                    $orderline['sku'] = $orderItem->sku;
                    $orderline['qty'] = $orderItem->qty;
                }

                $orderline['price'] = $orderItem->price_ex_vat;
                $orderline['profit_cost'] = $orderItem->profit_cost;
                $orderArray['lines'][] = $orderline;
                $count++;
            }
            $orders[] = $orderArray;
            $order->exact = 1;
            $order->save();

            return response()->json($orders);
        }

        return response()->json(['success' => 'success'], 200);
    }

    /**
     * When automatic creation of Transus
     *
     * @param Order $order
     * @return void
     */
    public function createTransusMessage(Order $order)
    {
        $shipment = $order->shipment;
        $transus = $order->transus;
        $transmissionVVA = substr($shipment->shipment_number, -5);
        $originalXML = simplexml_load_string($transus->message);
        $articles = '';
        $deliveryDate = substr($order->delivery_date, 0, 10);
        $picklistDate = str_replace('-', '', $deliveryDate);
        foreach ($originalXML->Message->Article as $article) {
            $articleXML = "<Article>
                              <LineNumber>" . $article->LineNumber . "</LineNumber>
                              <ArticleCodeSupplier>" . $article->ArticleCodeSupplier . "</ArticleCodeSupplier>
                              <GTIN>" . $article->GTIN . "</GTIN>
                              <OrderedQuantity>" . $article->OrderedQuantity . "</OrderedQuantity>
                              <DeliveredQuantity>" . $article->OrderedQuantity . "</DeliveredQuantity>
                              <ArticleDescription>" . $article->ArticleDescription . "</ArticleDescription>
                            </Article>";
            $articles .= $articleXML;
        }
        $transusXML = "<?xml version='1.0' encoding='UTF-8'?>
                            <Messages>
                              <Message>
                                <MessageStandard>TRANSUSXML</MessageStandard>
                                <MessageType>6</MessageType>
                                <IsTestMessage>N</IsTestMessage>
                                <OrderNumberBuyer>" . $originalXML->Message->OrderNumberBuyer . "</OrderNumberBuyer>
                                <RequestedDeliveryDate>" . $originalXML->message->RequestedDeliveryDate . "</RequestedDeliveryDate>
                                <DespatchAdviceNumber>" . $transmissionVVA . "</DespatchAdviceNumber>
                                <DespatchAdviceDate>" . str_replace('-', '',$picklistDate) . "</DespatchAdviceDate>
                                <PackingSlipNumber>" . $transmissionVVA . "</PackingSlipNumber>
                                <DeliveryDate>" . str_replace('-', '',$picklistDate) . "</DeliveryDate>
                                <BuyerGLN>" . $originalXML->Message->BuyerGLN . "</BuyerGLN>
                                <SupplierGLN>" . $originalXML->Message->SupplierGLN . "</SupplierGLN>
                                <DeliveryPartyGLN>" . $originalXML->Message->DeliveryPartyGLN . "</DeliveryPartyGLN>
                                <UltimateConsigneeGLN></UltimateConsigneeGLN>
                                " . $articles . "
                              </Message>
                            </Messages>";
        TransusMessage::create([
            'message' => $transusXML
        ]);
    }

    public function saveInvoice(Request $request)
    {
        $comlog = new Comlog();
        $comlog->message = $request->getContent();
        $comlog->save();

//        $this->parseInvoice($comlog);
        return response()->json(['success' => 'success'], 200);
    }

    public function parseComlog(Comlog $comlog)
    {
        $this->parseInvoice($comlog);
    }

    public function parseComlogs()
    {
        $comlogs = Comlog::where('parsed', 0)->get();
        if ($comlogs) {
            foreach ($comlogs as $comlog) {
                $this->parseComlog($comlog);
            }
        }
    }

    public function parseInvoice($comlog)
    {
        $raw = $comlog->message;
        $firstStep = str_replace("\\", '', $raw);
        $secondStep = str_replace('["', '', $firstStep);
        $thirdStep = str_replace('"]', '', $secondStep);
        $invoiceJson = json_decode($thirdStep);
        $invoiceObjects = reset($invoiceJson->InvoiceNumber);

        foreach ($invoiceObjects as $invoiceObject) {
            $order = Order::find($invoiceObject->Reference);
            if (!$order) {
                continue;
            }

            if (!$order->hasInvoice) {
                $invoice = Invoice::create([
                    'order_id' => $order->id,
                    'invoice_nr' => $invoiceObject->InvoiceNumber,
                    'order_nr_exact' => $invoiceObject->OrdernrExact,
                    'vat_prct' => $invoiceObject->VATPercentage,
                    'vat_amount' => $invoiceObject->VATAmount,
                    'total' => $invoiceObject->InvoiceAmount,
                    'net_line_amount' => $invoiceObject->NetLineAmountT,
                    'invoice_date' => str_replace('T', ' ', $invoiceObject->InvoiceDate)
                ]);
                foreach ($invoiceObject->Regel as $invoiceLine) {
                    $invoice->invoiceitem()->create([
                        'sku' => $invoiceLine->ArticleCode,
                        'article_net_price' => $invoiceLine->ArticleNetPrice,
                        'article_unit_code' => $invoiceLine->UnitCode,
                        'article_code_supplier' => $invoiceLine->ArticleCode,
                        'vat_base_amount' => $invoiceLine->VATBaseAmount,
                        'vat_percentage' => $invoiceObject->VATPercentage,
                        'invoiced_qty' => $invoiceLine->InvoicedQuantity,
                        'gtin' => 123,
                        'net_line_amount' => $invoiceLine->NetLineAmount
                    ]);
                    if ($invoiceLine->Order_id) {
                        if($invoiceLine->Order_id != $order->id) {
                            $attributes = ['order_id' => $invoiceLine->Order_id];
                            $values = ['invoice_id' => $invoice->id];

                            InvoiceLink::firstOrCreate($attributes, $values);
                        }
                    }
                }
                if ($order->customer->id == 10551) {
                    $this->sendHornbachInvoice($invoice);
                }
                if ($order->customer->id == 10441) {
                    $this->sendWelkoopInvoice($invoice);
                }
            }
        }

        $comlog->parsed = 1;
        $comlog->save();
    }

    public function sendHornbachInvoice(Invoice $invoice)
    {
        $transus = $invoice->order->transus;
        $originalXML = simplexml_load_string($transus->message);
        $articles = '';
        $lineNumber = 1;
        foreach ($invoice->invoiceitem as $invoiceItem) {
            $product = Product::where('sku', $invoiceItem->sku)->first();
            $articleXML = "<Article>
                                      <LineNumber>" . $lineNumber . "</LineNumber>
                                      <ArticleNetPrice>" . $invoiceItem->article_net_price . "</ArticleNetPrice>
                                      <ArticleNetPriceUnitCode>PCS</ArticleNetPriceUnitCode>
                                      <ArticleCodeSupplier>" . $invoiceItem->article_code_supplier . "</ArticleCodeSupplier>
                                      <VATBaseAmount>" . $invoiceItem->vat_base_amount . "</VATBaseAmount>
                                      <VATPercentage>21.00</VATPercentage>
                                      <IsDutyFree>N</IsDutyFree>
                                      <GTIN>" . $product->gtin . "</GTIN>
                                      <InvoicedQuantity>" . $invoiceItem->invoiced_qty . "</InvoicedQuantity>
                                      <NetLineAmount>" . $invoiceItem->net_line_amount . "</NetLineAmount>                                      
                                    </Article>";
            $articles .= $articleXML;
            $lineNumber++;
        }
        $transusXML = "<?xml version='1.0' encoding='UTF-8'?>
                                <Messages>
                                  <Message>
                                    <MessageStandard>TRANSUSXML</MessageStandard>
                                    <MessageType>8</MessageType>
                                    <BuyerGLN>" . $originalXML->Message->BuyerGLN . "</BuyerGLN>
                                    <CurrencyCode>EUR</CurrencyCode>
                                    <DeliveryPartyGLN>" . $originalXML->Message->DeliveryPartyGLN . "</DeliveryPartyGLN>
                                    <DeliveryDate>" . str_replace('00:00:00', '', str_replace('-', '', $invoice->order->delivery_date)) . "</DeliveryDate>
                                    <InvoiceDate>" . str_replace('00:00:00', '', str_replace('-', '', $invoice->invoice_date)) . "</InvoiceDate>
                                    <InvoiceeGLN>" . $originalXML->Message->InvoiceeGLN . "</InvoiceeGLN>
                                    <InvoiceNumber>" . $invoice->invoice_nr . "</InvoiceNumber>
                                    <InvoiceType>NOR</InvoiceType>
                                    <OrderNumberBuyer>" . $originalXML->Message->OrderNumberBuyer . "</OrderNumberBuyer>
                                    <SupplierGLN>" . $originalXML->Message->SupplierGLN . "</SupplierGLN>
                                    <SupplierVATNumber>NL815064408B01</SupplierVATNumber>
                                    <IsTestMessage>N</IsTestMessage>
                                    <IsDutyFree>N</IsDutyFree>
                                    " . $articles . "
                                    <InvoiceTotals>
                                      <InvoiceAmount>" . $invoice->total . "</InvoiceAmount>
                                      <NetLineAmount>" . $invoice->total - $invoice->vat_amount . "</NetLineAmount>
                                      <VATAmount>" . $invoice->vat_amount . "</VATAmount>
                                    </InvoiceTotals>
                                    <InvoiceVATTotals>
                                      <VATPercentage>21.00</VATPercentage>
                                      <VATAmount>" . $invoice->vat_amount . "</VATAmount>
                                      <VATBaseAmount>" . $invoice->total - $invoice->vat_amount . "</VATBaseAmount>
                                      <IsDutyFree>N</IsDutyFree>
                                    </InvoiceVATTotals>
                                  </Message>
                                </Messages>";
        TransusMessage::create([
            'message' => $transusXML
        ]);
        $invoice->sent = 1;
        $invoice->save();
    }

    public function sendWelkoopInvoice(Invoice $invoice)
    {
        $transus = $invoice->order->transus;
        $originalXML = simplexml_load_string($transus->message);
        $articles = '';
        $lineNumber = 1;
        foreach ($invoice->invoiceitem as $invoiceItem) {
            $product = Product::where('sku', $invoiceItem->sku)->first();
            $articleXML = "<Article>
                              <LineNumber>" . $lineNumber . "</LineNumber>
                              <ArticleNetPrice>" . $invoiceItem->article_net_price . "</ArticleNetPrice>
                              <ArticleNetPriceUnitCode>PCS</ArticleNetPriceUnitCode>
                              <ArticleCodeSupplier>" . $invoiceItem->article_code_supplier . "</ArticleCodeSupplier>
                              <VATBaseAmount>" . $invoiceItem->vat_base_amount . "</VATBaseAmount>
                              <VATPercentage>21.00</VATPercentage>
                              <IsDutyFree>N</IsDutyFree>
                              <GTIN>" . $product->gtin . "</GTIN>
                              <InvoicedQuantity>" . $invoiceItem->invoiced_qty . "</InvoicedQuantity>
                              <NetLineAmount>" . $invoiceItem->net_line_amount . "</NetLineAmount>                                      
                            </Article>";
            $articles .= $articleXML;
            $lineNumber++;
        }
        $shipment = $invoice->order->shipment;
        $transmissionVVA = substr($shipment->shipment_number, -5);
        $transusXML = "<?xml version='1.0' encoding='UTF-8'?>
                            <Messages>
                              <Message>
                                <MessageStandard>TRANSUSXML</MessageStandard>
                                <MessageType>8</MessageType>
                                <BuyerGLN>" . $originalXML->Message->BuyerGLN . "</BuyerGLN>
                                <CurrencyCode>EUR</CurrencyCode>
                                <DeliveryPartyGLN>" . $originalXML->Message->DeliveryPartyGLN . "</DeliveryPartyGLN>
                                <InvoiceDate>" . str_replace('00:00:00', '', str_replace('-', '', $invoice->invoice_date)) . "</InvoiceDate>
                                <InvoiceeGLN>" . $originalXML->Message->InvoiceeGLN . "</InvoiceeGLN>
                                <InvoiceNumber>" . $invoice->invoice_nr . "</InvoiceNumber>
                                <InvoiceType>NOR</InvoiceType>
                                <OrderNumberBuyer>" . $originalXML->Message->OrderNumberBuyer . "</OrderNumberBuyer>
                                <SupplierGLN>" . $originalXML->Message->SupplierGLN . "</SupplierGLN>
                                <DespatchAdviceNumber>" . $transmissionVVA . "</DespatchAdviceNumber>
                                <IsTestMessage>N</IsTestMessage>
                                <IsDutyFree>N</IsDutyFree>
                                " . $articles . "
                                <InvoiceTotals>
                                  <InvoiceAmount>" . $invoice->total . "</InvoiceAmount>
                                  <NetLineAmount>" . $invoice->total - $invoice->vat_amount . "</NetLineAmount>
                                </InvoiceTotals>
                                <InvoiceVATTotals>
                                  <VATPercentage>21.00</VATPercentage>
                                  <VATAmount>" . $invoice->vat_amount . "</VATAmount>
                                  <VATBaseAmount>" . $invoice->total - $invoice->vat_amount . "</VATBaseAmount>
                                  <IsDutyFree>N</IsDutyFree>
                                </InvoiceVATTotals>
                              </Message>
                            </Messages>";
        TransusMessage::create([
            'message' => $transusXML
        ]);
        $invoice->sent = 1;
        $invoice->save();
    }

    public function checkShipments()
    {
        $orders = Order::where('status', 'SHIPPED')
            ->whereDate('delivery_date', '<', now())
            ->get();
        foreach ($orders as $order) {
            $token = $this->transmissionLogin();
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $token,
                'Content-Type' => 'application/json',
            ])->get('https://api.trans-mission.nl/api/shipments/shipment_status/transport_number/' . $order->shipment->shipment_number);
            $response = $response->json();
            if (array_key_exists('data', $response)) {
                $shipmentStatus = $response['data'];
                foreach ($shipmentStatus as $status) {
                    if($status['status_description'] == 'Afgehandeld') {
                        $order->status = 'COMPLETED';
                        $order->save();
                        continue;
                    }
                }
            } else {
                Notification::route('slack', "*******************************************************************************")
                    ->notify(new SlackNotification('Transmission Shipment status kan niet worden opgehaald : ' . $order->id));
            }
        }
    }

    public function checkShipmentStatus(Order $order)
    {
        $token = $this->transmissionLogin();
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $token,
            'Content-Type' => 'application/json',
        ])->get('https://api.trans-mission.nl/api/shipments/shipment_status/transport_number/' . $order->shipment->shipment_number);
        $response = $response->json();
        $shipmentStatus = $response['data'];

        foreach ($shipmentStatus as $status) {
            if($status['status_description'] == 'Afgehandeld') {
                $order->status = 'COMPLETED';
                $order->save();
                continue;
            }
        }
    }

    public function ordersOfInterest()
    {
        $sevenDaysAgo = Carbon::now()->subDays(7);
        $twoDaysAgo = Carbon::now()->subDays(2);
        $oneDayAgo = Carbon::now()->subDays(1);

        $notificationMsg = '';

        $orders = Order::query()
            ->where(function ($query) use ($sevenDaysAgo) {
                $query->whereIn('customer_id', [10551, 10441])
                    ->where('order_date', '<', $sevenDaysAgo)
                    ->whereNotIn('status', ['SHIPPED','COMPLETED','CANCELLED','RETURNED']);
            })
            ->orWhere(function ($query) use ($twoDaysAgo) {
                $query->whereDate('delivery_date', '<', $twoDaysAgo)
                    ->whereNotIn('status', ['SHIPPED','COMPLETED','CANCELLED','RETURNED']);
            })
            ->orWhere(function ($query) use ($oneDayAgo) {
                $query->whereDate('delivery_date', '<', $oneDayAgo)
                    ->where('status', 'READY_FOR_SHIPPING');
            })
            ->get();
        if (!$orders) {
            return;
        }
        foreach ($orders as $order) {
            $notificationMsg .= $order->customer->name . ' -- ' . $order->id . PHP_EOL;
            $notificationMsg .= 'Besteldatum: ' . $order->order_date . PHP_EOL;
            $notificationMsg .= 'Status: ' . $order->status . PHP_EOL;
            $notificationMsg .= PHP_EOL;
        }
        Notification::route('slack', "*******************************************************************************")
            ->notify(new SlackNotification('De volgende bestellings moeten bekeken worden : '. PHP_EOL . $notificationMsg));
    }

    public function transmissionLogin()
    {
        $expiresAt = Cache::get('api_token_expires_at');
        $now = now();

        if (Cache::get('api_token') && $now->lessThan($expiresAt)) {
            return Cache::get('api_token');
        }

        $username = Settings::where('key', 'transmission_username')->first();
        $password = Settings::where('key', 'transmission_password')->first();

        $response = Http::asForm()->post('https://api.trans-mission.nl/api/login', [
            'user' => $username->value,
            'password' => $password->value
        ]);

        // Check for a successful response
        if ($response['status'] == 200 && $response['result_code'] == 'ok') {
            $token = $response['access_token'];
            $expiresIn = $response['expires_in'];

            $expiresAt = $now->addSeconds($expiresIn);

            Cache::put('api_token', $token);
            Cache::put('api_token_expires_at', $expiresAt);

            return $token;
        }

    }

    public function regenPicklist() {
        $picklistItems = Picklist::where('picked', 0)->get();
        foreach ($picklistItems as $picklistItem) {
            $customerId = $picklistItem->orderitem->entireOrder->customer->id;
            $picklistItem->customer = $customerId;
            $picklistItem->save();
        }
    }

    public function parseWoocommerce()
    {
        $yesterday = Carbon::yesterday()->format('Y-m-d');
        $response = Http::withBasicAuth(
            'ck_793be03188f432cc72b8c7dc494a2a9b24f980bc',
            'cs_ac49088b6b72cb52d616a3e50cc41ea0294d3418')
            ->get('https://www.bustotaal.nl/wp-json/wc/v3/orders?status=completed&modified_after=' . $yesterday . 'T00:00:00&modified_before=' . $yesterday . 'T23:59:59');
        $orders = $response->json();

        foreach ($orders as $order) {
            foreach ($order['line_items'] as $lineItem) {
                if ($lineItem['price'] == 0 || substr($lineItem['sku'], 0, 2) == 'MW') {
                    continue;
                }

                Woocommerce::create([
                    'order_id' => $order['number'],
                    'sku' => $lineItem['sku'],
                    'qty' => $lineItem['quantity']
                ]);
            }
        }
    }

    public function parseWooCommerceStock()
    {
        $orders = Woocommerce::where('parsed', 0)->get();

        foreach ($orders as $order) {
            $product = Product::where('sku', $order->sku)->first();
            if ($product) {

            }
        }

    }
}
