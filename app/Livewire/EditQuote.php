<?php

namespace App\Livewire;

use App\Models\Quote;
use App\Models\QuoteLine;
use Livewire\Component;

class EditQuote extends Component
{
    public $quoteId;
    public $quote;
    public $showEditProductModal = false;
    public $productName;
    public $productDesc;
    public $productQty;
    public $productQtyType;
    public $price;
    public $currentProduct;
    public $selectedQuoteLine;

    protected $listeners = ['quoteLineUpdated' => '$refresh'];

    public function mount($quote_id)
    {
        $this->quoteId = $quote_id;
        $this->quote = Quote::find($quote_id);
    }

    public function editQuoteLine($id)
    {
        $this->dispatch('productModalOpened');
        $this->selectedQuoteLine = QuoteLine::find($id);
        $this->currentProduct = $this->selectedQuoteLine->product;
        $this->productName = $this->selectedQuoteLine->name;
        $this->productDesc = $this->selectedQuoteLine->line_desc;
        $this->productQty = $this->selectedQuoteLine->qty;
        $this->productQtyType = $this->selectedQuoteLine->qty_type;
        $this->price = $this->selectedQuoteLine->price;

        $this->showEditProductModal = true;
    }

    public function updateProductLine()
    {
        $this->selectedQuoteLine->name = $this->productName;
        $this->selectedQuoteLine->qty = $this->productQty;
        $this->selectedQuoteLine->qty_type = $this->productQtyType;
        $this->selectedQuoteLine->price = $this->price;

        $this->selectedQuoteLine->save();

        $this->showEditProductModal = false;

        $this->dispatch('quoteLineUpdated');
    }

    public function removeQuoteLine()
    {
        $this->selectedQuoteLine->delete();
        $this->dispatch('quoteLineUpdated');
        $this->showEditProductModal = false;
    }

    public function render()
    {
        return view('livewire.edit-quote');
    }
}
