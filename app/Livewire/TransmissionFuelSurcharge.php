<?php

namespace App\Livewire;

use App\Models\Settings;
use Livewire\Component;

class TransmissionFuelSurcharge extends Component
{
    public $fuelSurcharge;

    public function mount()
    {
        $fuelSurcharge = Settings::where('key', 'transmission_fuelsurcharge')->first();
        $this->fuelSurcharge = $fuelSurcharge ? $fuelSurcharge->value : null;
    }

    public function saveFuelSurcharge()
    {
        if ($this->fuelSurcharge) {
            Settings::updateOrCreate(
                ['key' => 'transmission_fuelsurcharge'],
                [
                    'key' => 'transmission_fuelsurcharge',
                    'value' => $this->fuelSurcharge
                ]
            );
        }

        $this->dispatch('banner-message', ['style' => 'success', 'message' => 'Opgeslagen.']);
    }

    public function render()
    {
        return view('livewire.transmission-fuel-surcharge');
    }
}
