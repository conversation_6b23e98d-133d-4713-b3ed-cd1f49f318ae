<?php

namespace App\Livewire;

use App\Models\Picklist as PicklistModel;
use App\Models\Transus;
use Carbon\Carbon;
use Illuminate\Support\Facades\Http;
use Livewire\Component;

class PicklistDate extends Component
{
    public $picklistDate;

    public function mount()
    {
        $this->picklistDate = Carbon::now()->format('Y-m-d');
    }

    public function updatedPicklistDate()
    {
        $this->dispatch('picklistDate', $this->picklistDate);
    }

    /**
     * Dispatching browser event so javascript can print picklist
     *
     * @return void
     */
    public function print()
    {
        $this->dispatch('print');
    }

    /**
     * Print labels to Zebra printer.
     * Emit completeOrders to Picklist::handleCompleteOrders
     *
     * @return void
     */
    public function printLabels()
    {
        $this->pickListItems = PicklistModel::whereDate('pick_date', $this->picklistDate)->where('picked', 0)->get();
        try {
            $fp=pfsockopen("***************",9100);
            foreach ($this->pickListItems as $item) {
               $order = $item->orderitem->entireOrder;
               $shipment = $order->shipment;
                if ($shipment) {
                    if ($shipment->shipped == 0) {
                        fputs($fp,base64_decode($shipment->label));
                        $shipment->shipped = 1;
                        $shipment->save();
                    }
                }
                $item->picked = 1;
                $item->save();
            }
            fclose($fp);
        } catch (Exception $e) {
            echo '{"status": "error", "message": "' . $e->getMessage() . '"}';
        }
        $this->dispatch('completeOrders', $this->picklistDate);
    }

    public function render()
    {
        return view('livewire.picklist-date');
    }
}
