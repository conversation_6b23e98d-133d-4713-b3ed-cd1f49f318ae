<?php

namespace App\Livewire;

use App\Models\Address;
use Illuminate\Support\Facades\Http;
use Livewire\Component;
use Illuminate\Database\Eloquent\Collection;

class OrderAddressSearch extends Component
{
    protected $listeners = ['customerSelected' => 'handleCustomerSelected'];

    public $search = '';
    public $selectedCustomer;
    public $selectedAddress;
    public $showModal = false;
    public $companyName;
    public $name;
    public $street;
    public $housenumber;
    public $postalCode;
    public $city;
    public $country;
    public $email;
    public $addresses;
    public $telephone;
    public $gln;
    public $addressSearchError;

    public function mount()
    {
        $this->addresses = new Collection();
    }

    public function handleCustomerSelected($customer)
    {
        $this->selectedCustomer = $customer;
    }

    public function pickup()
    {
        $this->selectedAddress = 'pickup';
        $this->dispatch('shippingAddress', 'pickup');
    }

    public function updatedSearch()
    {
        $this->addresses = Address::where('customer_id', $this->selectedCustomer)
            ->where(function($query) {
                $query->where('company_name', 'like', '%' . $this->search . '%')
                    ->orWhere('name', 'like', '%' . $this->search . '%');
            })->get();
    }

    public function searchAddress()
    {
        $this->addressSearchError = NULL;
        $response = Http::withBasicAuth('klRJCL6fxAXQNfdJ7S2F4SM8DGyr30gi7b3MijWClLk', '5N8gwvIE09u8H29HuggWOpOVHWmyGhnHofQUMfNeXzKGxPReWK')
            ->get('https://api.postcode.eu/nl/v1/addresses/postcode/' . urlencode(str_replace(' ', '',$this->postalCode)) . '/' . urlencode($this->housenumber));
        $address = $response->json();

        if (!array_key_exists('exception', $address)) {
            $this->street = $address['street'];
            $this->city = $address['city'];
            $this->country = 'NL';
        } else {
            $this->addressSearchError = 'Adres niet gevonden met deze Postcode en huisnummer.';
        }
    }

    public function selectAddress(Address $address)
    {
        $this->selectedAddress = $address;
        $this->dispatch('shippingAddress', $this->selectedAddress);
        $this->search = '';
        $this->addresses = new Collection();
    }

    public function saveAddress()
    {
        $this->selectedAddress = Address::create([
           'type' => 'shipping',
           'company_name' => $this->companyName,
           'name' => $this->name,
           'street' => $this->street,
           'housenumber' => $this->housenumber,
           'postal_code' => $this->postalCode,
           'city' => $this->city,
           'country' => $this->country,
           'email' => $this->email,
           'telephone' => $this->telephone,
           'gln' => $this->gln,
           'customer_id' => $this->selectedCustomer
        ]);
        $this->dispatch('shippingAddress', $this->selectedAddress);
        $this->showModal = false;
    }

    public function render()
    {
        return view('livewire.order-address-search');
    }
}
