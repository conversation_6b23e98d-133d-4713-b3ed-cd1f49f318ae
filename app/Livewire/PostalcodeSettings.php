<?php

namespace App\Livewire;

use App\Models\Settings;
use Livewire\Component;

class PostalcodeSettings extends Component
{
    public $postcodesZV;
    public $postcodesWE;
    public $postcodesNorth;

    public function mount()
    {
        $poscodesNorth = Settings::where('key', 'postcodes_north')->first();
        $this->postcodesNorth = $poscodesNorth ? $poscodesNorth->value : null;
        $poscodesZV = Settings::where('key', 'postcodes_zv')->first();
        $this->postcodesZV = $poscodesZV ? $poscodesZV->value : null;
        $poscodesWE = Settings::where('key', 'postcodes_we')->first();
        $this->postcodesWE = $poscodesWE ? $poscodesWE->value : null;
    }

    public function savePostalcodes()
    {
        if ($this->postcodesNorth) {
            Settings::updateOrCreate(
                ['key' => 'postcodes_north'],
                [
                    'key' => 'postcodes_north',
                    'value' => $this->postcodesNorth
                ]
            );
        }

        if ($this->postcodesZV) {
            Settings::updateOrCreate(
                ['key' => 'postcodes_zv'],
                [
                    'key' => 'postcodes_zv',
                    'value' => $this->postcodesZV
                ]
            );
        }

        if ($this->postcodesWE) {
            Settings::updateOrCreate(
                ['key' => 'postcodes_we'],
                [
                    'key' => 'postcodes_we',
                    'value' => $this->postcodesWE
                ]
            );
        }
        $this->dispatch('banner-message', ['style' => 'success', 'message' => 'Opgeslagen.']);
    }

    public function render()
    {
        return view('livewire.postalcode-settings');
    }
}
