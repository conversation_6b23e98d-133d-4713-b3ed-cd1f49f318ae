<?php

namespace App\Livewire;

use App\Models\Pricelist;
use Livewire\Component;
use Yajra\DataTables\Facades\DataTables;

class PricelistTable extends Component
{
    public $showModal = false;
    public $name;
    public $from;
    public $to;

    public function createPriceList()
    {
        $pricelist = Pricelist::create([
            'name' => $this->name,
            'from' => $this->from,
            'to' => $this->to
        ]);

        return redirect()->route('pricelist.edit', ['pricelist' => $pricelist->id]);
    }

    public function getPricelists()
    {
        $query = Pricelist::query()->withCount('customers');

        return DataTables::of($query)
            ->addColumn('customer_count', function($pricelist) {
                return $pricelist->customers_count;
            })
            ->make(true);
    }

    public function render()
    {
        return view('livewire.pricelist-table');
    }
}
