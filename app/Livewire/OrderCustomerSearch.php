<?php

namespace App\Livewire;

use App\Models\Customer;
use Illuminate\Database\Eloquent\Collection;
use Livewire\Component;

class OrderCustomerSearch extends Component
{
    public $search = '';
    public $customers;
    public $selectedCustomer;

    public function mount()
    {
        $this->customers = new Collection();
    }

    public function updatedSearch()
    {
        $this->customers = Customer::where('name', 'like', '%'.$this->search.'%')->get();
    }

    public function selectCustomer(Customer $customer)
    {
        $this->selectedCustomer = $customer;
        $this->dispatch('customerSelected', $customer->id);
        $this->dispatch('orderLog', '<li>Klant geslecteerd: ' . $customer->exact_id . ' - ' . $customer->name . '</li>');

        $this->search = '';
        $this->customers = new Collection();
    }

    public function render()
    {
        return view('livewire.order-customer-search');
    }
}
