<?php

namespace App\Livewire;

use App\Models\BaseProduct;
use App\Models\Product;
use Illuminate\Database\Eloquent\Collection;
use Livewire\Component;

class ProductBaseProduct extends Component
{
    public $productId;
    public $product;
    public $showModal = false;
    public $search = '';
    public $baseProducts;
    public $selectedBaseProduct;
    public $qty;
    public $revenueProduct;
    public $stockProduct;
    public $perQty;

    public function mount($productId)
    {
        $this->product = Product::find($productId);
        $this->baseProducts = new Collection();
    }

    public function updatedSearch()
    {
        $this->baseProducts = BaseProduct::where('name', 'like', '%'.$this->search.'%')->get();
    }

    public function selectBaseProduct(BaseProduct $baseProduct)
    {
        $this->selectedBaseProduct = $baseProduct;
    }

    public function attachBaseProduct()
    {
        if (!$this->revenueProduct) {
            $this->revenueProduct = 0;
        }
        $this->product->baseProducts()->attach($this->selectedBaseProduct->id, ['quantity' => $this->qty, 'revenue' => $this->revenueProduct, 'stock' => $this->stockProduct ?: false, 'per_qty' => $this->perQty]);
        $this->product = Product::find($this->productId);
        $this->resetFields();
    }

    public function removeBaseProduct($baseProductId)
    {
        $this->product->baseProducts()->detach($baseProductId);
        $this->product = Product::find($this->productId);
    }

    public function resetFields()
    {
        $this->selectedBaseProduct = null;
        $this->qty = null;
        $this->baseProducts = new Collection();
        $this->search = '';
        $this->perQty = null;
        $this->revenueProduct = false;
        $this->stockProduct = false;
        $this->showModal = false;
    }

    public function closeModal()
    {
        $this->resetFields();
    }

    public function render()
    {
        return view('livewire.product-base-product');
    }
}
