<?php

namespace App\Livewire;

use App\Models\Invoice;
use App\Models\InvoiceLink;
use App\Models\Product;
use App\Models\TransusMessage;
use Livewire\Component;

class OrderInvoice extends Component
{
    public $orderId;
    public $invoice;
    public $groupedInvoice = false;

    public function mount()
    {
        $this->invoice = Invoice::where('order_id', $this->orderId)->first();
        if (!$this->invoice) {
            $grouped = InvoiceLink::where('order_id', $this->orderId)->first();
            if ($grouped) {
                $this->groupedInvoice = true;
                $this->invoice = Invoice::find($grouped->invoice_id);
            }
        }
    }

    public function sendWelkoopInvoice()
    {
        if (!$this->invoice->order->trasus) {
            $this->createTransusInvoice();

            return;
        }
        $transus = $this->invoice->order->transus;
        $originalXML = simplexml_load_string($transus->message);
        $articles = '';
        $lineNumber = 1;
        foreach ($this->invoice->invoiceitem as $invoiceItem) {
            $product = Product::where('sku', $invoiceItem->sku)->first();
            $articleXML = "<Article>
                              <LineNumber>" . $lineNumber . "</LineNumber>
                              <ArticleNetPrice>" . $invoiceItem->article_net_price . "</ArticleNetPrice>
                              <ArticleNetPriceUnitCode>PCS</ArticleNetPriceUnitCode>
                              <ArticleCodeSupplier>" . $invoiceItem->article_code_supplier . "</ArticleCodeSupplier>
                              <VATBaseAmount>" . $invoiceItem->vat_base_amount . "</VATBaseAmount>
                              <VATPercentage>21.00</VATPercentage>
                              <IsDutyFree>N</IsDutyFree>
                              <GTIN>" . $product->gtin . "</GTIN>
                              <InvoicedQuantity>" . $invoiceItem->invoiced_qty . "</InvoicedQuantity>
                              <NetLineAmount>" . $invoiceItem->net_line_amount . "</NetLineAmount>                                      
                            </Article>";
            $articles .= $articleXML;
            $lineNumber++;
        }
        $shipment = $this->invoice->order->shipment;
        $transmissionVVA = substr($shipment->shipment_number, -5);
        $transusXML = "<?xml version='1.0' encoding='UTF-8'?>
                            <Messages>
                              <Message>
                                <MessageStandard>TRANSUSXML</MessageStandard>
                                <MessageType>8</MessageType>
                                <BuyerGLN>" . $originalXML->Message->BuyerGLN . "</BuyerGLN>
                                <CurrencyCode>EUR</CurrencyCode>
                                <DeliveryPartyGLN>" . $originalXML->Message->DeliveryPartyGLN . "</DeliveryPartyGLN>
                                <InvoiceDate>" . str_replace('00:00:00', '', str_replace('-', '', $this->invoice->invoice_date)) . "</InvoiceDate>
                                <InvoiceeGLN>" . $originalXML->Message->InvoiceeGLN . "</InvoiceeGLN>
                                <InvoiceNumber>" . $this->invoice->invoice_nr . "</InvoiceNumber>
                                <InvoiceType>NOR</InvoiceType>
                                <OrderNumberBuyer>" . $originalXML->Message->OrderNumberBuyer . "</OrderNumberBuyer>
                                <SupplierGLN>" . $originalXML->Message->SupplierGLN . "</SupplierGLN>
                                <DespatchAdviceNumber>" . $transmissionVVA . "</DespatchAdviceNumber>
                                <IsTestMessage>N</IsTestMessage>
                                <IsDutyFree>N</IsDutyFree>
                                " . $articles . "
                                <InvoiceTotals>
                                  <InvoiceAmount>" . $this->invoice->total . "</InvoiceAmount>
                                  <NetLineAmount>" . $this->invoice->total - $this->invoice->vat_amount . "</NetLineAmount>
                                </InvoiceTotals>
                                <InvoiceVATTotals>
                                  <VATPercentage>21.00</VATPercentage>
                                  <VATAmount>" . $this->invoice->vat_amount . "</VATAmount>
                                  <VATBaseAmount>" . $this->invoice->total - $this->invoice->vat_amount . "</VATBaseAmount>
                                  <IsDutyFree>N</IsDutyFree>
                                </InvoiceVATTotals>
                              </Message>
                            </Messages>";
        TransusMessage::create([
            'message' => $transusXML
        ]);
        $this->invoice->sent = 1;
        $this->invoice->save();
    }

    public function createTransusInvoice()
    {
        $articles = '';
        $lineNumber = 1;
        foreach ($this->invoice->invoiceitem as $invoiceItem) {
            $product = Product::where('sku', $invoiceItem->sku)->first();
            $articleXML = "<Article>
                              <LineNumber>" . $lineNumber . "</LineNumber>
                              <ArticleNetPrice>" . $invoiceItem->article_net_price . "</ArticleNetPrice>
                              <ArticleNetPriceUnitCode>PCS</ArticleNetPriceUnitCode>
                              <ArticleCodeSupplier>" . $invoiceItem->article_code_supplier . "</ArticleCodeSupplier>
                              <VATBaseAmount>" . $invoiceItem->vat_base_amount . "</VATBaseAmount>
                              <VATPercentage>21.00</VATPercentage>
                              <IsDutyFree>N</IsDutyFree>
                              <GTIN>" . $product->gtin . "</GTIN>
                              <InvoicedQuantity>" . $invoiceItem->invoiced_qty . "</InvoicedQuantity>
                              <NetLineAmount>" . $invoiceItem->net_line_amount . "</NetLineAmount>                                      
                            </Article>";
            $articles .= $articleXML;
            $lineNumber++;
        }
        $shipment = $this->invoice->order->shipment;
        if ($shipment) {
            $transmissionVVA = substr($shipment->shipment_number, -5);
        } else {
            $transmissionVVA = $this->invoice->order->id;
        }
        $transusXML = "<?xml version='1.0' encoding='UTF-8'?>
                            <Messages>
                              <Message>
                                <MessageStandard>TRANSUSXML</MessageStandard>
                                <MessageType>8</MessageType>
                                <BuyerGLN>8712181000008</BuyerGLN>
                                <CurrencyCode>EUR</CurrencyCode>
                                <DeliveryPartyGLN>" . $this->invoice->order->address->gln . "</DeliveryPartyGLN>
                                <InvoiceDate>" . str_replace('00:00:00', '', str_replace('-', '', $this->invoice->invoice_date)) . "</InvoiceDate>
                                <InvoiceeGLN>8712181000008</InvoiceeGLN>
                                <InvoiceNumber>" . $this->invoice->invoice_nr . "</InvoiceNumber>
                                <InvoiceType>NOR</InvoiceType>
                                <OrderNumberBuyer>" . $this->invoice->order->reference . "</OrderNumberBuyer>
                                <SupplierGLN>8719326141923</SupplierGLN>
                                <DespatchAdviceNumber>" . $transmissionVVA . "</DespatchAdviceNumber>
                                <IsTestMessage>N</IsTestMessage>
                                <IsDutyFree>N</IsDutyFree>
                                " . $articles . "
                                <InvoiceTotals>
                                  <InvoiceAmount>" . $this->invoice->total . "</InvoiceAmount>
                                  <NetLineAmount>" . $this->invoice->total - $this->invoice->vat_amount . "</NetLineAmount>
                                </InvoiceTotals>
                                <InvoiceVATTotals>
                                  <VATPercentage>21.00</VATPercentage>
                                  <VATAmount>" . $this->invoice->vat_amount . "</VATAmount>
                                  <VATBaseAmount>" . $this->invoice->total - $this->invoice->vat_amount . "</VATBaseAmount>
                                  <IsDutyFree>N</IsDutyFree>
                                </InvoiceVATTotals>
                              </Message>
                            </Messages>";
        TransusMessage::create([
            'message' => $transusXML
        ]);
        $this->invoice->sent = 1;
        $this->invoice->save();
    }

    public function sendHornbachInvoice()
    {
        $transus = $this->invoice->order->transus;
        $originalXML = simplexml_load_string($transus->message);
        $articles = '';
        $lineNumber = 1;
        foreach ($this->invoice->invoiceitem as $invoiceItem) {
            $product = Product::where('sku', $invoiceItem->sku)->first();
            $articleXML = "<Article>
                                      <LineNumber>" . $lineNumber . "</LineNumber>
                                      <ArticleNetPrice>" . $invoiceItem->article_net_price . "</ArticleNetPrice>
                                      <ArticleNetPriceUnitCode>PCS</ArticleNetPriceUnitCode>
                                      <ArticleCodeSupplier>" . $invoiceItem->article_code_supplier . "</ArticleCodeSupplier>
                                      <VATBaseAmount>" . $invoiceItem->vat_base_amount . "</VATBaseAmount>
                                      <VATPercentage>21.00</VATPercentage>
                                      <IsDutyFree>N</IsDutyFree>
                                      <GTIN>" . $product->gtin . "</GTIN>
                                      <InvoicedQuantity>" . $invoiceItem->invoiced_qty . "</InvoicedQuantity>
                                      <NetLineAmount>" . $invoiceItem->net_line_amount . "</NetLineAmount>                                      
                                    </Article>";
            $articles .= $articleXML;
            $lineNumber++;
        }
        $transusXML = "<?xml version='1.0' encoding='UTF-8'?>
                                <Messages>
                                  <Message>
                                    <MessageStandard>TRANSUSXML</MessageStandard>
                                    <MessageType>8</MessageType>
                                    <BuyerGLN>" . $originalXML->Message->BuyerGLN . "</BuyerGLN>
                                    <CurrencyCode>EUR</CurrencyCode>
                                    <DeliveryPartyGLN>" . $originalXML->Message->DeliveryPartyGLN . "</DeliveryPartyGLN>
                                    <DeliveryDate>" . str_replace('00:00:00', '', str_replace('-', '', $this->invoice->order->delivery_date)) . "</DeliveryDate>
                                    <InvoiceDate>" . str_replace('00:00:00', '', str_replace('-', '', $this->invoice->invoice_date)) . "</InvoiceDate>
                                    <InvoiceeGLN>" . $originalXML->Message->InvoiceeGLN . "</InvoiceeGLN>
                                    <InvoiceNumber>" . $this->invoice->invoice_nr . "</InvoiceNumber>
                                    <InvoiceType>NOR</InvoiceType>
                                    <OrderNumberBuyer>" . $originalXML->Message->OrderNumberBuyer . "</OrderNumberBuyer>
                                    <SupplierGLN>" . $originalXML->Message->SupplierGLN . "</SupplierGLN>
                                    <SupplierVATNumber>NL815064408B01</SupplierVATNumber>
                                    <IsTestMessage>N</IsTestMessage>
                                    <IsDutyFree>N</IsDutyFree>
                                    " . $articles . "
                                    <InvoiceTotals>
                                      <InvoiceAmount>" . $this->invoice->total . "</InvoiceAmount>
                                      <NetLineAmount>" . $this->invoice->total - $this->invoice->vat_amount . "</NetLineAmount>
                                      <VATAmount>" . $this->invoice->vat_amount . "</VATAmount>
                                    </InvoiceTotals>
                                    <InvoiceVATTotals>
                                      <VATPercentage>21.00</VATPercentage>
                                      <VATAmount>" . $this->invoice->vat_amount . "</VATAmount>
                                      <VATBaseAmount>" . $this->invoice->total - $this->invoice->vat_amount . "</VATBaseAmount>
                                      <IsDutyFree>N</IsDutyFree>
                                    </InvoiceVATTotals>
                                  </Message>
                                </Messages>";
        TransusMessage::create([
            'message' => $transusXML
        ]);
        $this->invoice->sent = 1;
        $this->invoice->save();
    }

    public function render()
    {
        return view('livewire.order-invoice');
    }
}
