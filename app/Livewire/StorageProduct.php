<?php

namespace App\Livewire;

use App\Models\Product;
use Livewire\Component;
use Illuminate\Database\Eloquent\Collection;

class StorageProduct extends Component
{
    public $product;
    public $products;
    public $search = '';
    public $selectedStorageProduct;
    public $isStorageProduct = false;
    public $showModal = false;
    public $quantity;

    public function mount($productId)
    {
        $this->product = Product::with('associatedProducts')->find($productId);
        if ($this->product->associatedProducts->isNotEmpty()) {
            $this->isStorageProduct = true;
        }

        if ($this->product->storage_product) {
            $this->selectedStorageProduct = Product::find($this->product->storage_product);
        }
        $this->products = new Collection();
    }

    public function updatedSearch()
    {
        if ($this->search === '') {
            $this->products = new Collection();
        } else {
            $this->products = Product::where('name', 'like', '%' . $this->search . '%')
                ->orWhere('sku', 'like', '%' . $this->search . '%')
                ->get();
        }
    }

    public function selectProduct(Product $product)
    {
        $this->search = '';
        $this->selectedStorageProduct = $product;
        $this->showModal = true;
    }

    public function saveStorageProduct()
    {
        $this->product->storage_product_qty = $this->quantity;
        $this->product->storage_product = $this->selectedStorageProduct->id;
        $this->product->save();
        $this->showModal = false;
    }

    public function removeStorageProduct()
    {
        $this->product->storage_product = NULL;
        $this->selectedStorageProduct = NULL;
        $this->product->save();
    }

    public function render()
    {
        return view('livewire.storage-product');
    }
}
