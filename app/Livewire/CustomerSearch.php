<?php

namespace App\Livewire;

use App\Models\Customer;
use App\Models\CustomerPrice;
use Livewire\Component;
use Illuminate\Database\Eloquent\Collection;
use Carbon\Carbon;

class CustomerSearch extends Component
{
    public $search = '';
    public $customers;
    public $selectedCustomer;
    public $productId;
    public $customerPrices;
    public $showModal = false;
    public $showModalEdit = false;
    public $showModalDelete = false;
    public $newPrice;
    public $fromQty;
    public $toQty;
    public $from;
    public $to;
    public $errorMessage = '';
    public $selectedTier;

    public function mount($productId)
    {
        $this->customers = new Collection();
        $this->productId = $productId;
    }

    public function updatedSearch()
    {
        $this->customers = Customer::where('name', 'like', '%'.$this->search.'%')->get();
    }

    public function selectCustomer(Customer $customer)
    {
        $this->selectedCustomer = $customer;
        $this->customerPrices = $customer->productPrices->where('product_id', $this->productId)->sortBy('from_qty');

        $this->search = '';
        $this->customers = new Collection();
    }

    public function addCustomerPrice()
    {
        $this->validate([
            'newPrice' => 'required|numeric',
        ]);

        $fromDate = $this->from ? Carbon::parse($this->from) : null;
        $toDate = $this->to ? Carbon::parse($this->to) : null;

        foreach ($this->customerPrices as $existingCustomerPrice) {
            $existingFromDate = $existingCustomerPrice->from ? Carbon::parse($existingCustomerPrice->from) : null;
            $existingToDate = $existingCustomerPrice->to ? Carbon::parse($existingCustomerPrice->to) : null;

            $isOverlappingQty = ((int)$this->fromQty >= $existingCustomerPrice->from_qty && (int)$this->fromQty <= $existingCustomerPrice->to_qty) ||
                ((int)$this->toQty >= $existingCustomerPrice->from_qty && (int)$this->toQty <= $existingCustomerPrice->to_qty);

            $isOverlappingDate = (!$fromDate && !$toDate) ||
                (!$existingFromDate && !$existingToDate) ||
                ($fromDate && $toDate && $existingFromDate && $existingToDate && $fromDate->between($existingFromDate, $existingToDate) || $toDate->between($existingFromDate, $existingToDate));

            if ($isOverlappingQty && $isOverlappingDate) {
                $this->errorMessage = 'Deze staffel overlapt met een reeds aanwezige straffel.';
                return;
            }
        }

        CustomerPrice::create([
            'customer_id' => $this->selectedCustomer->id,
            'product_id' => $this->productId,
            'price' => $this->newPrice,
            'from_qty' => $this->fromQty,
            'to_qty' => $this->toQty,
            'from' => Carbon::parse($this->from),
            'to' => Carbon::parse($this->to)
        ]);

        $this->selectedCustomer = Customer::with('productPrices')->find($this->selectedCustomer->id);
        $this->customerPrices = $this->selectedCustomer->productPrices->where('product_id', $this->productId);

        $this->resetInputFields();
        $this->showModal = false;
    }

    public function resetInputFields()
    {
        $this->newPrice = null;
        $this->fromQty = null;
        $this->toQty = null;
        $this->from = null;
        $this->to = null;
    }

    public function showDeleteTier($id)
    {
        $this->selectedTier = $id;
        $this->showModalDelete = true;
    }

    public function deleteTier()
    {
        $tier = CustomerPrice::find($this->selectedTier);
        $tier->delete();

        $this->selectedCustomer = Customer::with('productPrices')->find($this->selectedCustomer->id);
        $this->customerPrices = $this->selectedCustomer->productPrices->where('product_id', $this->productId);

        $this->selectedTier = null;
        $this->showModalDelete = false;
    }

    public function render()
    {
        return view('livewire.customer-search');
    }
}
