<?php

namespace App\Livewire;

use App\Models\Order;
use App\Models\Picklist;
use Livewire\Component;

class OrderStatus extends Component
{
    public $status;
    public $orderId;

    public function complete()
    {
        $order = Order::find($this->orderId);
        $order->status = 'COMPLETED';
        $order->save();
        $this->status = 'COMPLETED';
    }

    public function cancel()
    {
        $order = Order::find($this->orderId);

        foreach($order->orderitems as $orderItem) {
            $picklistItem = Picklist::where('order_item_id', $orderItem->id)->first();
            if ($picklistItem) {
                $picklistItem->delete();
            }
        }

        $order->status = 'CANCELLED';
        $order->save();
        $this->status = 'CANCELLED';
    }

    public function render()
    {
        return view('livewire.order-status');
    }
}
