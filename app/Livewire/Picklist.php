<?php

namespace App\Livewire;

use App\Models\Order;
use App\Models\Transus;
use App\Models\TransusMessage;
use Carbon\Carbon;
use Illuminate\Support\Facades\Http;
use Livewire\Component;
use App\Models\Picklist as PicklistModel;
use Illuminate\Support\Collection;

class Picklist extends Component
{
    public $pickDate;
    public $pickListItems;
    public $pickedItems;
    public $pickedListItems;
    public $welkoopPicklistItems;
    public $hornbachPicklistItems;
    public $otherPicklistItems;

    protected $listeners = [
        'picklistDate' => 'handlePicklistDateChanged',
        'completeOrders' => 'handleCompleteOrders'
    ];

    public function mount()
    {
        $this->pickDate = Carbon::now()->format('Y-m-d');
        $this->loadPickItems();
        $this->loadPickedItems();
    }

    /**
     * When picklist labels are printed, set status of the order to SHIPPED.
     * When order is from Transus, also create a Transus message to verify shipment.
     * Function is called from PicklistDate::printLabels
     *
     * @param $picklistDate
     * @return void
     */
    public function handleCompleteOrders($picklistDate)
    {
        foreach ($this->pickListItems as $item) {
            $order = Order::find($item->orderitem->order_id);
            if ($order->status !== Order::STATUS_SHIPPED) {
                $order->status = Order::STATUS_SHIPPED;
                $order->is_shipped = 1;
                $order->save();
                $transus = $order->transus;
                if ($transus) {
                    $shipment = $order->shipment;
                    $transmissionVVA = substr($shipment->shipment_number, -5);
                    $originalXML = simplexml_load_string($transus->message);
                    $articles = '';
                    foreach ($originalXML->Message->Article as $article) {
                        $articleXML = "<Article>
                                      <LineNumber>" . $article->LineNumber . "</LineNumber>
                                      <ArticleCodeSupplier>" . $article->ArticleCodeSupplier . "</ArticleCodeSupplier>
                                      <GTIN>" . $article->GTIN . "</GTIN>
                                      <OrderedQuantity>" . $article->OrderedQuantity . "</OrderedQuantity>
                                      <DeliveredQuantity>" . $article->OrderedQuantity . "</DeliveredQuantity>
                                      <ArticleDescription>" . $article->ArticleDescription . "</ArticleDescription>
                                    </Article>";
                        $articles .= $articleXML;
                    }
                    $deliveryPartyGln = $originalXML->Message->BuyerGLN;
                    if ($originalXML->Message->DeliveryPartyGLN) {
                        $deliveryPartyGln = $originalXML->Message->DeliveryPartyGLN;
                    }
                    $transusXML = "<?xml version='1.0' encoding='UTF-8'?>
                                <Messages>
                                  <Message>
                                    <MessageStandard>TRANSUSXML</MessageStandard>
                                    <MessageType>6</MessageType>
                                    <IsTestMessage>N</IsTestMessage>
                                    <OrderNumberBuyer>" . $originalXML->Message->OrderNumberBuyer . "</OrderNumberBuyer>
                                    <RequestedDeliveryDate>" . $originalXML->message->RequestedDeliveryDate . "</RequestedDeliveryDate>
                                    <DespatchAdviceNumber>" . $transmissionVVA . "</DespatchAdviceNumber>
                                    <DespatchAdviceDate>" . str_replace('-', '',$picklistDate) . "</DespatchAdviceDate>
                                    <PackingSlipNumber>" . $transmissionVVA . "</PackingSlipNumber>
                                    <DeliveryDate>" . str_replace('-', '',$picklistDate) . "</DeliveryDate>
                                    <BuyerGLN>" . $originalXML->Message->BuyerGLN . "</BuyerGLN>
                                    <SupplierGLN>" . $originalXML->Message->SupplierGLN . "</SupplierGLN>
                                    <DeliveryPartyGLN>" . $originalXML->Message->DeliveryPartyGLN . "</DeliveryPartyGLN>
                                    <UltimateConsigneeGLN></UltimateConsigneeGLN>
                                    " . $articles . "
                                  </Message>
                                </Messages>";
                    TransusMessage::create([
                        'message' => $transusXML
                    ]);
                }
            }
        }
        if ($this->pickListItems) {
            $this->dispatch('banner-message', ['style' => 'success', 'message' => 'Bestellingen status SHIPPED.']);
        } else {
            $this->dispatch('banner-message', ['style' => 'danger', 'message' => 'Geen bestellingen op af te ronden.']);
        }
    }

    /**
     * When picklist date changes reload the picklist items
     *
     * @param $pickDate
     * @return void
     */
    public function handlePicklistDateChanged($pickDate)
    {
        $this->pickDate = $pickDate;
        $this->loadPickItems();
        $this->loadPickedItems();
    }

    /**
     * Load picklist items in three arrays.
     * Hornbach, Welkoop and Other
     *
     * @return void
     */
    public function loadPickItems()
    {
        $date = Carbon::createFromFormat('Y-m-d', $this->pickDate);
        $this->pickListItems = PicklistModel::whereDate('pick_date', $date)->where('picked', 0)->get();

        $this->welkoopPicklistItems = $this->pickListItems->where('customer', 10441)
            ->groupBy('sku')
            ->map(function ($group) {
                return [
                    'name' => $group->first()->name,
                    'total_qty' => $group->sum('qty'),
                ];
            })->sortBy('name')->toArray();

        $this->hornbachPicklistItems = $this->pickListItems->where('customer', 10551)
            ->groupBy('sku')
            ->map(function ($group) {
                return [
                    'name' => $group->first()->name,
                    'total_qty' => $group->sum('qty'),
                ];
            })->sortBy('name')->toArray();

        $this->otherPicklistItems = $this->pickListItems->reject(function ($item) {
            return $item->customer == 10441 || $item->customer == 10551;
        })->groupBy('sku')->map(function ($group) {
            return [
                'name' => $group->first()->name,
                'total_qty' => $group->sum('qty'),
            ];
        })->sortBy('name')->toArray();
    }

    /**
     * Picklist items for picked date that have already been picked
     *
     * @return void
     */
    public function loadPickedItems()
    {
        $date = Carbon::createFromFormat('Y-m-d', $this->pickDate);
        $this->pickedListItems = PicklistModel::whereDate('pick_date', $date)->where('picked', 1)->get();

        $this->pickedItems = $this->pickedListItems->groupBy('sku')->map(function (Collection $group) {
            return [
                'name' => $group->first()->name,
                'total_qty' => $group->sum('qty'),
            ];
        })->sortBy('name')->toArray();

    }

    public function reprintLabels()
    {
        $printItems = [];

        foreach ($this->pickedListItems as $item) {
            $order = $item->orderitem->entireOrder;
            $shipment = $order->shipment;
            if ($shipment) {
                if (!array_key_exists($shipment->id, $printItems)) {
                    $printItems[$shipment->id] = $shipment->label;
                }
            }
        }

        try {
            $fp=pfsockopen("***************",9100);
            foreach ($printItems as $printItem) {
                fputs($fp,base64_decode($printItem));
            }
            fclose($fp);
        } catch (Exception $e) {
            echo '{"status": "error", "message": "' . $e->getMessage() . '"}';
        }
    }

    public function render()
    {
        return view('livewire.picklist');
    }
}
