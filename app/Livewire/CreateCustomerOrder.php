<?php

namespace App\Livewire;

use App\Models\Address;
use App\Models\Customer;
use App\Models\Order;
use App\Models\Product;
use App\Notifications\SlackNotification;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Notification;
use Laravel\Jetstream\InteractsWithBanner;
use Livewire\Component;

class CreateCustomerOrder extends Component
{
    use InteractsWithBanner;

    public $name;
    public $street;
    public $houseNumber;
    public $postalCode;
    public $city;
    public $country;
    public $email;
    public $telephone;
    public $search = '';
    public $showModal = false;
    public $products;
    public $errorMessage;
    public $orderlineProduct = null;
    public $selectedProduct;
    public $quantity;
    public $orderlines = [];
    public $orderError;
    public $deliveryDate;
    public $reference;

    public function mount()
    {
        $this->products = Product::where('dealer_visible', 1)->get();
    }

    public function updatedSelectedProduct($selectedProduct)
    {
        $this->orderlineProduct = Product::find($selectedProduct);
    }

    public function updatedQuantity()
    {
        $this->errorMessage = null;
    }

    public function closeModal()
    {
        $this->showModal = false;
        $this->selectedProduct = null;
        $this->orderlineProduct = null;
        $this->quantity = null;
    }

    public function addOrderLine()
    {
        if ($this->quantity % $this->orderlineProduct->transport_qty != 0) {
            $this->errorMessage = 'Dit aantal is niet deelbaar door ' . $this->orderlineProduct->transport_qty;

            return;
        }

        $this->orderlines[] = [
            'product_id' => $this->orderlineProduct->id,
            'product_name' => $this->orderlineProduct->name,
            'transport_qty' => $this->orderlineProduct->transport_qty,
            'quantity' => $this->quantity,
            'sku' => $this->orderlineProduct->sku,
        ];

        $this->closeModal();
    }

    public function saveOrder()
    {
        // create new address
        $address = Address::create([
            'type' => 'shipping',
            'name' => $this->name,
            'street' => $this->street,
            'housenumber' => $this->houseNumber,
            'postal_code' => str_replace(' ', '', $this->postalCode),
            'city' => $this->city,
            'country' => $this->country,
            'email' => $this->email,
            'telephone' => $this->telephone,
        ]);

        $customer = Customer::find(Auth::user()->customer_id);

        $order = new Order();
        $order->customer_id = $customer->id;
        $order->user_id = Auth::id();
        $order->status = 'PROCESSING';
        $order->address_id = $address->id;
        $order->order_date = Carbon::now()->format('Y-m-d');
        $order->delivery_date = $this->deliveryDate;
        $order->shipper = 'transmission';
        $order->reference = $this->reference;
        $order->save();

        // create the order
//        $order = Order::create([
//            'address_id' => $address->id,
//            'customer_id' => Auth::user()->customer_id,
//            'user_id' => Auth::id(),
//            'status' => 'PROCESSING',
//            //'cost' => $this->subTotalCost,
//            //'transport_cost' => $this->transportCost,
//            //'total_cost' => $this->subTotalCost + $this->transportCost,
//            //'subtotal' => $this->subTotal,
//            //'total_ex_vat' => $this->total,
//            //'transport_price' => $this->transport,
//            //'vat' => ($this->total / 100) * 21,
//            //'total_incl_vat' => $this->total,
//            'delivery_date' => $this->deliveryDate,
//            'order_date' => Carbon::now()->format('Y-m-d'),
//            'shipper' => 'transmission',
//            'reference' => $this->reference
//        ]);

        // calculate prices and cost
        // calculate transmission price and cost
        Notification::route('slack', "*******************************************************************************")
            ->notify(new SlackNotification('Een dealer heeft een nieuwe bestelling geplaatst'));
    }

    public function createOrderline(Order $order)
    {

    }


    public function render()
    {
        return view('livewire.create-customer-order');
    }
}
