<?php

namespace App\Livewire;

use App\Models\Product;
use Livewire\Component;

class NewProduct extends Component
{
    public $type = 'retail';
    public $name;
    public $sku;
    public $description;
    public $cost;
    public $price;
    public $gtin = NULL;
    public $transportQty;

    public function saveProduct()
    {
        $rules = [
            'name' => 'required|string',
            'sku' => 'required|string',
            'description' => 'required|string',
            'cost' => 'required|numeric',
            'price' => 'required|numeric',
            'transportQty' => 'required|integer',
            'gtin' => 'nullable|string'
        ];

       $this->validate($rules);

        $product = Product::create([
            'name' => $this->name,
            'sku' => $this->sku,
            'description' => $this->description,
            'cost' => $this->cost,
            'price' => $this->price,
            'gtin' => $this->gtin,
            'transport_qty' => $this->transportQty,
            'transport_unit' => 'BLOCK',
            'source' => $this->type,
        ]);

        return redirect()->route('product.edit', $product->id);
    }

    public function render()
    {
        return view('livewire.new-product');
    }
}
