<?php

namespace App\Livewire;

use App\Models\BaseProduct;
use Livewire\Component;

class BaseProductEdit extends Component
{
    public $baseProduct;
    public $name;
    public $price;

    public function mount(BaseProduct $baseProduct)
    {
        $this->baseProduct = $baseProduct;
        $this->name = $baseProduct->name;
        $this->price = $baseProduct->price;
    }

    public function save()
    {
        $this->baseProduct->name = $this->name;
        $this->baseProduct->price = $this->price;
        $this->baseProduct->save();

        return redirect()->route('product.base');
    }

    public function render()
    {
        return view('livewire.base-product-edit');
    }
}
