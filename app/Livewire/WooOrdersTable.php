<?php

namespace App\Livewire;


use App\Models\Woocommerce as WoocommerceAlias;
use Livewire\Component;
use Ya<PERSON>ra\DataTables\Facades\DataTables;

class WooOrdersTable extends Component
{
    public function getOrders()
    {
        $query = WoocommerceAlias::get();

        return DataTables::of($query)
            ->make(true);
    }

    public function render()
    {
        return view('livewire.woo-orders-table');
    }
}
