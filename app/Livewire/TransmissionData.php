<?php

namespace App\Livewire;

use App\Models\Product;
use Livewire\Component;

class TransmissionData extends Component
{
    public $product;
    public $weight;
    public $width;
    public $height;
    public $description;
    public $length;
    public $unitType;
    public $byProduct = false;

    public function mount($productId)
    {
        $this->product = Product::with('transmissionData')->find($productId);
        $this->byProduct = $this->product->is_by_product;
        if ($this->product->transmissionData) {
            $this->weight = $this->product->transmissionData->transport_weight;
            $this->length = $this->product->transmissionData->length;
            $this->width = $this->product->transmissionData->width;
            $this->height = $this->product->transmissionData->height;
            $this->description = $this->product->transmissionData->description;
            $this->unitType = $this->product->transmissionData->unit_type;
        }

    }

    public function saveTransmissionData()
    {
        $transmissionData = $this->product->transmissionData;
        if (!$transmissionData) {
            $this->product->transmissionData()->create([
                'transport_weight' => $this->weight,
                'length' => $this->length,
                'width' => $this->width,
                'height' => $this->height,
                'description' => $this->description,
                'unit_type' => $this->unitType
            ]);
        } else {
            $transmissionData->transport_weight = $this->weight;
            $transmissionData->length = $this->length;
            $transmissionData->width = $this->width;
            $transmissionData->height = $this->height;
            $transmissionData->description = $this->description;
            $transmissionData->unit_type = $this->unitType;
            $transmissionData->save();
        }

        $this->product->is_by_product = $this->byProduct;
        $this->product->save();

        $this->dispatch('banner-message', ['style' => 'success', 'message' => 'Transmission Data opgeslagen.']);
    }

    public function render()
    {
        return view('livewire.transmission-data');
    }
}
