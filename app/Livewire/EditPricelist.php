<?php

namespace App\Livewire;

use App\Models\CustomerPrice;
use App\Models\Pricelist;
use App\Models\Product;
use Carbon\Carbon;
use Livewire\Component;

class EditPricelist extends Component
{
    public $products;
    public $showModal = false;
    public $pricelist;
    public $productPrices = [];
    public $dropShipProductPrices = [];
    public $fromQty;
    public $toQty;
    public $newPrice;
    public $productId;
    public $errorMessage;
    public $from;
    public $to;
    public $checkboxValues = [];
    public $dropShipCheckboxValues = [];
    public $type;
    public $productName;
    public $product;

    public function mount($pricelistId)
    {
        $this->pricelist = Pricelist::with(['priceTiers', 'customers'])->find($pricelistId);
        $this->createProductPriceArray();
        $this->from = Carbon::parse($this->pricelist->from)->format('Y-m-d');
        $this->to = Carbon::parse($this->pricelist->to)->format('Y-m-d');
        $this->products = Product::all();
    }

    public function updatedFrom()
    {
        $this->pricelist->from = $this->from;
        $this->pricelist->save();
    }

    public function updatedTo()
    {
        $this->pricelist->to = $this->to;
        $this->pricelist->save();
    }

    public function createProductPriceArray()
    {
        foreach ($this->pricelist->priceTiers as $priceTier) {

            if ($priceTier['type'] == 'D') {
                $this->dropShipCheckboxValues[$priceTier->product_id] = $priceTier->free_shipping;
                $this->dropShipProductPrices[$priceTier->product_id][$priceTier->id] = [
                    'id' => $priceTier->id,
                    'from_qty' => $priceTier->from_qty,
                    'to_qty' => $priceTier->to_qty,
                    'price' => $priceTier->price
                ];
            } else {
                $this->checkboxValues[$priceTier->product_id] = $priceTier->free_shipping;
                $this->productPrices[$priceTier->product_id][$priceTier->id] = [
                    'id' => $priceTier->id,
                    'from_qty' => $priceTier->from_qty,
                    'to_qty' => $priceTier->to_qty,
                    'price' => $priceTier->price
                ];
            }
        }
    }

    public function addTier($id, $type)
    {
        $this->productId = $id;
        $product = Product::find($id);
        $this->productName = $product->name;
        $this->type = $type;
        $this->showModal = true;
    }

    function isOverlapping()
    {
        if (array_key_exists($this->productId, $this->productPrices)) {
            foreach ($this->productPrices[$this->productId] as $tierPrice) {
                if ($this->fromQty <= $tierPrice['to_qty'] && $this->toQty >= $tierPrice['from_qty']) {
                    $this->errorMessage = 'Deze hoeveelheden overlappen met een reeds bestaande staffel. '.$tierPrice['from_qty'].' - '.$tierPrice['to_qty'];

                    return true;
                }
            }
        }

        return false; // No overlap
    }

    public function saveTier()
    {
        if ($this->isOverlapping()) {
            return;
        }

        $freeShipping = false;
        if ($this->type === 'D') {
            if (array_key_exists($this->productId, $this->dropShipCheckboxValues)) {
                $freeShipping = $this->dropShipCheckboxValues[$this->productId];
            }
        } else {
            if (array_key_exists($this->productId, $this->checkboxValues)) {
                $freeShipping = $this->checkboxValues[$this->productId];
            }
        }

        $newPriceTier = $this->pricelist->priceTiers()->create([
            'product_id' => $this->productId,
            'from_qty' => $this->fromQty,
            'to_qty' => $this->toQty,
            'price' => $this->newPrice,
            'type' => $this->type,
            'free_shipping' => $freeShipping
        ]);

        if ($this->type === 'D') {
            $this->dropShipProductPrices[$this->productId][$newPriceTier->id] = [
                'id' => $newPriceTier->id,
                'from_qty' => $newPriceTier->from_qty,
                'to_qty' => $newPriceTier->to_qty,
                'price' => $newPriceTier->price
            ];
        } else {
            $this->productPrices[$this->productId][$newPriceTier->id] = [
                'id' => $newPriceTier->id,
                'from_qty' => $newPriceTier->from_qty,
                'to_qty' => $newPriceTier->to_qty,
                'price' => $newPriceTier->price
            ];
        }

        $this->productId = null;
        $this->fromQty = null;
        $this->toQty = null;
        $this->newPrice = null;
        $this->type = null;
        $this->showModal = false;
    }

    public function deletePricelist()
    {
        $this->pricelist->customers()->detach();
        $this->pricelist->delete();

        return redirect()->route('pricelist.index');
    }

    public function removeTier($productId, $tierId, $type)
    {
        $tier = CustomerPrice::find($tierId);
        $tier->delete();
        if ($type == 'D') {
            unset($this->dropShipProductPrices[$productId][$tierId]);
        } else {
            unset($this->productPrices[$productId][$tierId]);
        }
    }

    public function toggleFreeShipping($productId)
    {
        if (!array_key_exists($productId, $this->productPrices)) {
            $this->dispatch('banner-message', ['style' => 'danger', 'message' => 'Voeg eerst staffel(s) toe.']);
            $this->checkboxValues[$productId] = false;

            return;
        }
        $value = $this->checkboxValues[$productId];
        foreach ($this->productPrices[$productId] as $priceTier) {
            $savedPricetier = CustomerPrice::find($priceTier['id']);
            $savedPricetier->free_shipping = $value;
            $savedPricetier->save();
        }
    }

    public function toggleDropshipFreeShipping($productId)
    {
        if (!array_key_exists($productId, $this->dropShipProductPrices)) {
            $this->dispatch('banner-message', ['style' => 'danger', 'message' => 'Voeg eerst staffel(s) toe.']);
            $this->dropShipCheckboxValues[$productId] = false;

            return;
        }
        $value = $this->dropShipCheckboxValues[$productId];
        foreach ($this->dropShipProductPrices[$productId] as $priceTier) {
            $savedPricetier = CustomerPrice::find($priceTier['id']);
            $savedPricetier->free_shipping = $value;
            $savedPricetier->save();
        }
    }

    public function render()
    {
        return view('livewire.edit-pricelist');
    }
}
