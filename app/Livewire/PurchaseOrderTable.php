<?php

namespace App\Livewire;

use App\Models\PurchaseOrder;
use Livewire\Component;
use Yajra\DataTables\Facades\DataTables;

class PurchaseOrderTable extends Component
{
    public function render()
    {
        return view('livewire.purchase-order-table');
    }

    public function getPurchaseOrders()
    {
        $query = PurchaseOrder::with('customer', 'purchaseOrderlines');

        return DataTables::of($query)
            ->addColumn('products', function ($purchaseOrder) {
                $products = '';
                foreach ($purchaseOrder->purchaseOrderlines as $line) {
                    $products .= $line->product->name.', ';
                }

                return rtrim($products, ', ');
            })->make(true);
    }
}
