<?php

namespace App\Livewire;

use App\Models\Product;
use Illuminate\Http\JsonResponse;
use Livewire\Component;
use Yajra\DataTables\Facades\DataTables;

class ProductWooTable extends Component
{
    public function render()
    {
        return view('livewire.product-woo-table');
    }

    public function getProducts()
    {
        $query = Product::where('source', 'bustotaal')->with('customerPrices');

        return DataTables::of($query)
            ->addColumn('pricelists', function ($product) {
            $pricelists = [];
                $customerPrices = $product->customerPrices;

                foreach ($customerPrices as $customerPrice) {
                    if ($customerPrice->pricelist) {
                        $pricelists[] = $customerPrice->pricelist->name;
                    }
                }
            return implode(', ', $pricelists);
        })->make(true);
    }
}
