<?php

namespace App\Livewire;

use App\Models\Order;
use Livewire\Component;
use Yajra\DataTables\Facades\DataTables;

class OrderAllTable extends Component
{
    public function render()
    {
        return view('livewire.order-all-table');
    }

    public function getOrders()
    {
        $query = Order::with('customer', 'shipment', 'address');

        return DataTables::of($query)
            ->addColumn('hasShipment', function ($order) {
                if ($order->shipper === 'pickup') {
                    return true;
                }
                return $order->hasShipment;
            })
            ->make(true);
    }
}
