<?php

namespace App\Livewire;

use App\Models\BaseProduct;
use App\Models\BaseProductStock as BaseProductStockModel;
use Livewire\Component;

class BaseProductStock extends Component
{
    public $baseProduct;
    public $qty;
    public $cost;
    public $transportcost;
    public $showModal = false;
    public $baseProductStock;

    public function mount(BaseProduct $baseProduct)
    {
        $this->baseProduct = $baseProduct;
    }

    public function saveStockPlace()
    {
        if ($this->baseProductStock) {
            $this->baseProductStock->update([
                'quantity' => $this->qty,
                'price' => $this->cost,
                'transport_cost' => $this->transportcost / $this->qty,
            ]);
            $this->baseProductStock = NULL;
        } else {
            $this->baseProduct->stock()->create([
                'quantity' => $this->qty,
                'price' => $this->cost,
                'transport_cost' => $this->transportcost / $this->qty,
            ]);
        }

        $this->qty = null;
        $this->cost = null;
        $this->transportcost = null;
        $this->showModal = false;

        $this->baseProduct->load('stock');
    }

    public function cancelModal()
    {
        $this->showModal = false;
        $this->qty = null;
        $this->cost = null;
    }

    public function editStockPlace(BaseProductStockModel $baseProductStock)
    {
        $this->baseProductStock = $baseProductStock;
        $this->qty = $baseProductStock->quantity;
        $this->cost = $baseProductStock->price;
        $this->transportcost = $baseProductStock->transport_cost;
        $this->showModal = true;
    }

    public function removeStockPlace(BaseProductStockModel $baseProductStock)
    {
        $this->baseProduct->stock()->where('id', $baseProductStock->id)->delete();
        $this->baseProduct->load('stock');
    }

    public function render()
    {
        return view('livewire.base-product-stock');
    }
}
