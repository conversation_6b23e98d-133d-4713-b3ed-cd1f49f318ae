<?php

namespace App\Livewire;

use App\Models\Address;
use App\Models\Customer;
use App\Models\Order;
use App\Models\Product;
use App\Models\Transus;
use App\Models\Picklist;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Livewire\Component;
use Exception;

class TransusViewMessage extends Component
{
    public $transusId;
    public $transus;
    public $log;

    public function mount($transusId)
    {
        $this->transus = Transus::find($transusId);
    }

    public function reParseXml()
    {
        if ($this->transus->order_id) {
            $order = Order::find($this->transus->order_id);

            foreach ($order->orderitems as $orderItem) {
                $picklistItem = Picklist::where('order_item_id', $orderItem->id)->first();
                if ($picklistItem) {
                    $picklistItem->delete();
                }
            }

            $order->status = 'CANCELLED';
            $order->save();
        }
        $this->transus->reParseXml();
    }

    public function render()
    {
        return view('livewire.transus-view-message');
    }
}
