<?php

namespace App\Livewire;

use App\Models\Customer;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Carbon\Carbon;
use App\Models\TransmissionCost;

class CreateOrder extends Component
{
    protected $listeners = [
        'orderLines' => 'recalculate',
        'customerSelected' => 'handleCustomerSelected',
        'shippingAddress' => 'handleShippingAddress',
        'deliveryDate' => 'handleDeliveryDate',
        'reference' => 'handleReference',
        'orderDate' => 'handleOrderDate',
        'transportPrice' => 'handleTransportPrice',
        'transportCostPrice' => 'handleTransportCostPrice',
        'transmissionTransport' => 'handleTransmissionShipping',
        'orderLog' => 'handleOrderLog'
    ];

    public $orderLines;
    public $transportCost = 0.00;
    public $subTotal = 0.00;
    public $total = 0.00;
    public $totalQty = 0;
    public $subTotalCost = 0.00;
    public $transport = 0.00;
    public $totalCost = 0.00;
    public $selectedCustomer;
    public $transportPriceTier;
    public $transportLength = 0.00;
    public $postalCode;
    public $transportPriceTierQty;
    public $transportPriceTierPrice;
    public $shippingAddress;
    public $showSavingModal = false;
    public $deliveryDate;
    public $orderDate;
    public $shipper;
    public $orderLog = '';
    public $reference;
    public $country;
    public $transportQty;

    public function handleOrderLog($value)
    {
        $this->orderLog .= $value;
    }

    public function handleOrderDate($date)
    {
        $this->orderDate = $date;
        $this->orderLog .= '<li>Besteldatum aangepast naar: ' . $date . '</li>';
    }

    public function handleDeliveryDate($date)
    {
        $this->deliveryDate = $date;
        $this->orderLog .= '<li>Leverdatum geselecteerd: ' . $date . '</li>';
    }

    public function handleReference($reference)
    {
        $this->reference = $reference;
    }

    public function handleCustomerSelected($customerId)
    {
        $this->selectedCustomer = Customer::find($customerId);
    }

    public function handleShippingAddress($address)
    {
        $this->shippingAddress = $address;

        if (is_array($address)) {
            if ($address['country'] === 'NL') {
                $this->postalCode = (int)substr($address['postal_code'], 0, 4);
            } else {
                $this->postalCode = $address['postal_code'];
            }
            $this->country = $address['country'];
        } else {
            $this->shipper = 'pickup';
        }

        $this->orderLog .= '<li>Verzendadres geselecteerd.</li>';
    }

    public function handleTransportPrice($price)
    {
        $this->transport = (float) $price;
        $this->total += (float) $price;
        if ($this->shipper != 'pickup') {
            $this->shipper = 'other';
            $this->orderLog .= '<li>Verzending via Planning.</li><li>Verzendkosten toegevoegd: €' . $price . ' </li>';
        } else {
            $this->orderLog .= '<li>Afgehaald in Winsum</li>';
        }

    }

    public function handleTransportCostPrice($price)
    {
        $this->transportCost = (float) $price;
        $this->orderLog .= '<li>Kostprijs verzending €' . $price . '</li>';
    }

    public function handleTransmissionShipping()
    {
        $freeShipping = 0;
        foreach($this->orderLines as $orderLine) {
            $this->transportLength += $orderLine['transport_length'] * ($orderLine['qty'] / $orderLine['product']['transport_qty']);
            $freeShipping = $orderLine['free_shipping'];
        }

        $transmissionCost = TransmissionCost::where('trailer_length', '>=', $this->transportLength)
            ->orderBy('trailer_length', 'asc')
            ->first();
        $this->transportCost = $transmissionCost->getPriceForPostcode($this->postalCode, $this->country);
        $this->transportPriceTier = $this->selectedCustomer->getTransportPriceTier($this->totalQty);
        $this->transportPriceTierPrice = $this->transportPriceTier->getPriceForPostcode($this->postalCode, $this->country);
        $this->transportPriceTierQty = $this->transportPriceTier->trailer_spot;
        if ($freeShipping == 0) {
            $this->transport = $this->transportPriceTierPrice * $this->totalQty;
        }
        $this->shipper = 'transmission';

        $this->totalCost = $this->subTotalCost + $this->transportCost;
        $this->total = $this->subTotal + $this->transport;
    }

    public function recalculate($orderLines)
    {
        $this->orderLines = $orderLines;
        $this->totalQty = 0;

        $this->subTotalCost = 0.00;
        $this->subTotal = 0.00;
        $this->total = 0.00;
        $this->totalCost = 0.00;
        $this->transportLength = 0.00;
        foreach($this->orderLines as $orderline) {
            $this->totalQty += $orderline['qty'] / $orderline['product']['transport_qty'];
//            $this->subTotalCost += $orderline['cost'];
            $this->subTotal += $orderline['price'];
        }


        // TODO: selecteer datum
        // TODO: landcode BE hoeft niet postcode prijs op te halen.
        $this->totalCost = $this->subTotalCost + $this->transportCost;

        $this->total = $this->subTotal + $this->transport;
    }

    public function saveOrder()
    {
        try {
            $order = Order::create([
                'address_id' => is_array($this->shippingAddress) ? $this->shippingAddress['id'] : NULL,
                'customer_id' => $this->selectedCustomer->id,
                'user_id' => Auth::id(),
                'status' => $this->shipper == 'pickup' ? 'COMPLETED' : 'PROCESSING',
                'cost' => $this->subTotalCost,
                'transport_cost' => $this->transportCost,
                'total_cost' => $this->subTotalCost + $this->transportCost,
                'subtotal' => $this->subTotal,
                'total_ex_vat' => $this->total,
                'transport_price' => $this->transport,
                'vat' => ($this->total / 100) * 21,
                'total_incl_vat' => $this->total,
                'delivery_date' => $this->deliveryDate,
                'order_date' => $this->orderDate ?: Carbon::now()->format('Y-m-d'),
                'shipper' => $this->shipper,
                'reference' => $this->reference
            ]);
            $orderProfitCost = 0.00;

            $stockPlaces = [];
            $count = 1;
            foreach ($this->orderLines as $orderline) {
                $orderLineCost = 0.00;
                $profitCostProductCost = 0.00;
                $orderLineProfitCost  = 0.00;   
                $product = Product::find($orderline['product']['id']);

                $storageProduct = $product->baseStockProducts()->first();

                if ($storageProduct) {
                    $neededStock = $orderline['qty'] * $storageProduct->pivot->quantity;
                    $totalStock = 0;

                    // Calculate total available stock
                    foreach ($storageProduct->stock as $stockPlace) {
                        $totalStock += $stockPlace->quantity;
                    }

                    // Check if total stock is sufficient
                    if ($totalStock >= $neededStock) {
                        // Deduct stock from each stock place
                        foreach ($storageProduct->stock as $stockPlace) {
                            if ($neededStock == 0) {
                                break;
                            }

                            $takeStock = min($stockPlace->quantity, $neededStock);
                            $neededStock -= $takeStock;
                            $orderLineProfitCost += ($stockPlace->price * $takeStock);
                            $this->orderLog .= '<li>Winst/verlies berekening regel ' . $count . ': ' . $storageProduct->name . ' -- ' . $takeStock . ' X &euro;' . $stockPlace->price . '</li>';
                            $orderLineCost += $stockPlace->price * $takeStock;
                            $orderLineCost += $stockPlace->transport_cost * $takeStock;
                            $this->orderLog .= '<li>Transportkosten inkoop regel ' . $count . ': ' . $takeStock . ' X &euro;' . $stockPlace->transport_cost . '</li>';
                            // Minus stockplace and add to array for saving after order is created successfully.
                            $stockPlace->quantity -= $takeStock;

                            $stockPlaces[] = $stockPlace;
                        }
                    } else {
                        $this->orderLog .= '<li>Geen voorraad bij basisproduct. Standaard prijs van basisproduct als kostprijs.</li>';
                        $orderLineCost += $storageProduct->price * $neededStock;
                        $this->orderLog .= '<li>Berekening met standaard prijs van basisproduct. ' . $neededStock . ' X &euro;' . $storageProduct->price . '</li>';

//                        throw new \Exception('Niet genoeg voorraad voor ' . $storageProduct->name);
                    }
                } else {
//                    $this->dispatch('banner-message', ['style' => 'danger', 'message' => 'Dit product heeft geen voorraadproduct.']);
//                    return;
                }

                if (count($product->baseProducts) > 0) {
                    foreach ($product->baseProducts as $baseProduct) {
                        if (!$baseProduct->pivot->revenue) {
                            $baseCalcQty = $orderline['qty'] / $baseProduct->pivot->per_qty;
//                            $productCost = $baseProduct->price * $baseCalcQty;
                            $orderLineCost += $baseCalcQty * $baseProduct->price;
                            $this->orderLog .= '<li>Korstprijs berekening regel '.$count.': '.$baseProduct->name.' -- '.$baseCalcQty.' X &euro;'.$baseProduct->price.'</li>';
                        }
                    }

                } else {
                    $this->orderLog .= '<li>Oude kostprijs berekening regel ' . $count . ': ' . $orderline['qty'] . ' X €' . $product->cost . '</li>';
                    $orderLineCost = $product->cost * $orderline['qty'];
                }

                $orderItem = new OrderItem();
                $orderItem->product_id = $orderline['product']['id'];
                $orderItem->sku = $orderline['product']['sku'];
                $orderItem->qty = $orderline['qty'];
                $orderItem->product_name = $orderline['product']['name'];
                $orderItem->product_desc = $orderline['product']['description'];
                $orderItem->cost = $orderLineCost;
                $order->cost += $orderLineCost;
                $orderItem->price_ex_vat = $orderline['price'];
                $orderItem->order = $count;
                $orderItem->calculation = $orderline['price_calculation'];
                $orderItem->profit_cost = $orderLineProfitCost;
                $order->orderitems()->save($orderItem);
                $count++;
            }

            // Only update stock if order is created successfully
            foreach ($stockPlaces as $stockPlace) {
                $stockPlace->save();
            }

            $order->orderLog()->create([
                'log' => $this->orderLog
            ]);

            $order->total_cost += $order->cost;
            $order->revenue = $order->total_incl_vat - $order->total_cost;
            $order->profit_cost = $orderProfitCost;
            $order->save();

            return redirect()->route('order.edit', ['order' => $order->id]);
        } catch (\Exception $e) {
            $this->dispatch('banner-message', ['style' => 'danger', 'message' => 'Fout tijdens opslaan van de order.' . $e->getMessage()]);
        }
    }

    public function render()
    {
        return view('livewire.create-order');
    }
}
