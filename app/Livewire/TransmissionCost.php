<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\TransmissionCost as TransmissionCostModel;

class TransmissionCost extends Component
{
    public $transmissionCostTiers;
    public $showModal = false;
    public $errorMessage = '';
    public $trailerLength;
    public $priceNL;
    public $priceBE;
    public $priceZV;
    public $priceWE;
    public $priceNorth;
    public $fromDate;
    public $toDate;

    public function mount()
    {
        $this->transmissionCostTiers = TransmissionCostModel::orderBy('trailer_length')->get();
    }

    public function saveTier()
    {
        TransmissionCostModel::create([
            'trailer_length' => $this->trailerLength,
            'price_nl' => $this->priceNL,
            'price_be' => $this->priceBE,
            'price_zv' => $this->priceZV,
            'price_we' => $this->priceWE,
            'price_north' => $this->priceNorth,
            'from_date' => $this->fromDate,
            'to_date' => $this->toDate,
        ]);
        $this->transmissionCostTiers = TransmissionCostModel::orderBy('trailer_length')->get();

        $this->showModal = false;
        $this->errorMessage = '';
        $this->trailerLength = null;
        $this->priceNL = null;
        $this->priceBE = null;
        $this->priceZV = null;
        $this->priceWE = null;
        $this->priceNorth = null;
        $this->fromDate = null;
        $this->toDate = null;
    }

    public function render()
    {
        return view('livewire.transmission-cost');
    }
}
