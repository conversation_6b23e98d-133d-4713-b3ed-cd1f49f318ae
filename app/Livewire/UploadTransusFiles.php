<?php

namespace App\Livewire;

use App\Models\Address;
use App\Models\Customer;
use App\Models\Order;
use App\Models\Product;
use App\Models\Transus;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Storage;
use Livewire\Component;
use Livewire\WithFileUploads;

class UploadTransusFiles extends Component
{
    use WithFileUploads;

    public $xmlFiles = [];
    public $log;
    public $filesProcessed = 0;

    public function uploadAndProcess()
    {
        $this->validate([
            'xmlFiles.*' => 'required|file',
        ]);

        foreach ($this->xmlFiles as $xmlFile) {
            $path = $xmlFile->store('xmls');
            $originalFileName = pathinfo($xmlFile->getClientOriginalName(), PATHINFO_FILENAME);
            $this->parseXml($path, $originalFileName);
            $this->filesProcessed++;

            Storage::delete($path);
        }

        $this->xmlFiles = [];
    }

    private function parseXml($path, $originalFilename)
    {
        $xmlContent = Storage::get($path);
        $xml = simplexml_load_string($xmlContent);

        $transus = Transus::create([
            'message' => $xmlContent,
            'direction' => 'nvt',
            'parsed' => 0,
            'transus_id' => $originalFilename,
            'customer' => $xml->Message->Sender,
            'order_nr' => $xml->Message->OrderNumberBuyer
        ]);
        $transus->parseXml();
    }

    public function render()
    {
        return view('livewire.upload-transus-files');
    }
}
