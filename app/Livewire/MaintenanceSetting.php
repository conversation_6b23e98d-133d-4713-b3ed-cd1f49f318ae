<?php

namespace App\Livewire;

use App\Models\Settings;
use Livewire\Component;

class MaintenanceSetting extends Component
{
    public $maintenanceMode = false;

    public function mount()
    {
        $maintenance = Settings::where('key', 'maintenance_mode')->first();
        $this->maintenanceMode = $maintenance ? (bool)$maintenance->value : false;
    }

    public function updatedMaintenanceMode()
    {
        Settings::updateOrCreate(
            ['key' => 'maintenance_mode'],
            [
                'key' => 'maintenance_mode',
                'value' => $this->maintenanceMode
            ]
        );
    }

    public function render()
    {
        return view('livewire.maintenance-setting');
    }
}
