<?php

namespace App\Livewire;

use App\Models\TransportPriceAgreement;
use App\Models\TransportPricelist;
use Livewire\Component;
use Yajra\DataTables\Facades\DataTables;

class TransportPricelistTable extends Component
{
    public $showModal = false;
    public $name;
    public $from;
    public $to;

    public function createPriceList()
    {
        $pricelist = TransportPricelist::create([
            'name' => $this->name,
            'from' => $this->from,
            'to' => $this->to
        ]);

        return redirect()->route('transportpricelists.edit', ['pricelist' => $pricelist->id]);
    }

    public function getPricelists()
    {
        $query = TransportPricelist::query()->withCount('customers');

        return DataTables::of($query)
            ->addColumn('customer_count', function($transportPricelist) {
                return $transportPricelist->customers_count;
            })
            ->make(true);
    }

    public function render()
    {
        return view('livewire.transport-pricelist-table');
    }
}
