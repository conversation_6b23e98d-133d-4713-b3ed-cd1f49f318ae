<?php

namespace App\Livewire;

use App\Models\Order;
use Livewire\Component;
use Yajra\DataTables\Facades\DataTables;

class OrderFinishedTable extends Component
{
    public function render()
    {
        return view('livewire.order-finished-table');
    }

    public function getOrders()
    {
        $query = Order::with('customer', 'shipment', 'address')->where('status', 'COMPLETED');

        return DataTables::of($query)
            ->addColumn('hasShipment', function ($order) {
                if ($order->shipper === 'pickup') {
                    return true;
                }
                return $order->hasShipment;
            })
            ->addColumn('hasInvoice', function ($order) {
                return $order->hasInvoice;
            })
            ->addColumn('invoiceSent', function ($order) {
                if ($order->invoice) {
                    return $order->invoice->sent;
                }
                return 0;
            })
            ->make(true);
    }
}
