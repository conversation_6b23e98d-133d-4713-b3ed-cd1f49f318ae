<?php

namespace App\Livewire;

use App\Models\Order;
use Livewire\Component;
use Spatie\Activitylog\Models\Activity;

class ShowOrderActivities extends Component
{
    public $activities;
    public $selectedActivity = null;

    protected $rules = [
        'selectedActivity' => 'required|exists:activities,id',
    ];

    public function mount(Order $order)
    {
        $orderActivities = Activity::forSubject($order)->get();

        $orderItemActivities = $order->orderitems->map(function ($orderItem) {
            return Activity::forSubject($orderItem)->get();
        })->flatten();

        $this->activities = $orderActivities->concat($orderItemActivities)->sortByDesc('created_at');
    }

    public function showDetails($activityId)
    {
        $this->selectedActivity = Activity::find($activityId);
    }

    public function getChangedAttributes()
    {
        if (!$this->selectedActivity) return [];

        $changes = $this->selectedActivity->changes;
        $old = $changes['old'] ?? [];
        $attributes = $changes['attributes'] ?? [];

        return array_keys(
            array_diff_assoc($old, $attributes) + array_diff_assoc($attributes, $old)
        );
    }


    public function closeDetails()
    {
        $this->selectedActivity = null;
    }

    public function render()
    {
        return view('livewire.show-order-activities');
    }
}
