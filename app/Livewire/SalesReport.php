<?php

namespace App\Livewire;

use App\Models\Order;
use Livewire\Component;
use Illuminate\Support\Facades\Response;

class SalesReport extends Component
{
    public $fromDate;
    public $toDate;
    public $orders;
    public $totalSales = 0.00;
    public $totalCost = 0.00;
    public $totalSalesWelkoop = 0.00;
    public $pallets = 0;
    public $customers = [];
    public $products = [];
    public $hornbachPallets = 0;

    public function showRapport() 
    {
        $this->totalSales = 0.00;
        $this->totalSalesWelkoop = 0.00;
        $this->totalCost = 0.00;
        $this->pallets = 0;
        $this->hornbachPallets = 0;
        $this->customers = [];
        $this->products = [];
        $this->orders = Order::whereBetween('created_at', [$this->fromDate . ' 00:00:00', $this->toDate . ' 23:59:59'])->where('status', 'COMPLETED')->get();
        foreach ($this->orders as $order) {
            if ($order->customer->id == 10441) {
                $this->totalSalesWelkoop += $order->total_ex_vat;
            }
            $this->totalSales += $order->total_ex_vat;
            $this->totalCost += $order->total_cost;
            foreach ($order->orderitems as $orderitem) {
                $product = $orderitem->product;
                if (!$product->transport_qty) {
                    dd('Geen transport aantal bij product ' . $product->id . ' - ' . $product->sku);
                }
                $this->pallets += $orderitem->qty / $product->transport_qty;
                if ($order->customer->id == 10551) {
                    $this->hornbachPallets += $orderitem->qty / $product->transport_qty;
                }
                if (!array_key_exists($product->id, $this->products)) {
                    $this->products[$product->id] = [
                        'name' => $product->name,
                        'sku' => $product->sku,
                        'pallets' => $orderitem->qty / $product->transport_qty
                    ];
                } else {
                    $this->products[$product->id]['pallets'] += $orderitem->qty / $product->transport_qty;
                }
            }
            if (!array_key_exists($order->customer->id, $this->customers)) {
                $this->customers[$order->customer->id] = [
                    'name' => $order->customer->name,
                    'total_sales' => $order->total_ex_vat,
                    'total_cost' => $order->total_cost
                ];
            } else {
                $this->customers[$order->customer->id]['total_sales'] += $order->total_ex_vat;
                $this->customers[$order->customer->id]['total_cost'] += $order->total_cost;
            }
        }
        uasort($this->customers, function($a, $b) {
            return $b['total_sales'] <=> $a['total_sales'];
        });
        $this->customers = array_values($this->customers);

        uasort($this->products, function($a, $b) {
            return $b['pallets'] <=> $a['pallets'];
        });
        $this->products = array_values($this->products);

        $this->customers = array_slice($this->customers, 0, 10);
        $this->products = array_slice($this->products, 0, 10);
    }

    public function exportReport()
    {
        $orders = Order::whereBetween('delivery_date', [$this->fromDate . ' 00:00:00', $this->toDate . ' 23:59:59'])->get();
        $csvData = [];

        // Add the headers for the CSV
        $csvData[] = ['Order ID', 'Order Line ID', 'Klant ID', 'Product Name', 'Product SKU', 'Quantity', 'Price', 'Cost', 'Transport Cost', 'Transport Price', 'Delivery Date'];

        // Loop through each order and its order lines
        foreach ($orders as $order) {
            $orderItemsQty = count($order->orderitems);
            foreach ($order->orderitems as $orderLine) {
                $csvData[] = [
                    $order->id,
                    $orderLine->id,
                    $order->customer->exact_id,
                    $orderLine->product_name,
                    $orderLine->sku,
                    $orderLine->qty,
                    $orderLine->price_ex_vat,
                    $orderLine->cost,
                    $order->transport_cost / $orderItemsQty,
                    $order->transport_price / $orderItemsQty,
                    str_replace('00:00:00', '', $order->delivery_date),
                ];
            }
        }

        // Generate the CSV content
        $filename = 'orders_' . now()->format('Ymd_His') . '.csv';
        $handle = fopen('php://temp', 'r+');
        foreach ($csvData as $row) {
            fputcsv($handle, $row);
        }
        rewind($handle);
        $csvContent = stream_get_contents($handle);
        fclose($handle);

        // Return the CSV as a response for download
        return response()->streamDownload(function () use ($csvContent) {
            echo $csvContent;
        }, $filename, ['Content-Type' => 'text/csv']);
    }

    public function exportHornbach()
    {
        $orders = Order::whereBetween('delivery_date', [$this->fromDate . ' 00:00:00', $this->toDate . ' 23:59:59'])
            ->where('status', 'COMPLETED')
            ->where('customer_id', 10551)
            ->get();
        $csvData = [];

        // Add the headers for the CSV
        $csvData[] = ['Order ID', 'Order Line ID', 'Order ID Hornbach', 'Product Name', 'Product SKU', 'Pallets', 'Delivery Date', 'Delivery Country'];

        // Loop through each order and its order lines
        foreach ($orders as $order) {
            $orderItemsQty = count($order->orderitems);
            foreach ($order->orderitems as $orderLine) {
                $product = $orderLine->product;
                $csvData[] = [
                    $order->id,
                    $orderLine->id,
                    $order->reference,
                    $orderLine->product_name,
                    $orderLine->sku,
                    $orderLine->qty / $product->transport_qty,
                    str_replace('00:00:00', '', $order->delivery_date),
                    $order->address->country,
                ];
            }
        }

        // Generate the CSV content
        $filename = 'hornbach_pallets_' . now()->format('Ymd_His') . '.csv';
        $handle = fopen('php://temp', 'r+');
        foreach ($csvData as $row) {
            fputcsv($handle, $row);
        }
        rewind($handle);
        $csvContent = stream_get_contents($handle);
        fclose($handle);

        // Return the CSV as a response for download
        return response()->streamDownload(function () use ($csvContent) {
            echo $csvContent;
        }, $filename, ['Content-Type' => 'text/csv']);
    }

    public function print()
    {
        $this->dispatch('printsales');
    }

    public function render()
    {
        return view('livewire.sales-report');
    }
}
