<?php

namespace App\Livewire;

use App\Models\Product;
use App\Models\Quote;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Illuminate\Database\Eloquent\Collection;

class CreateQuote extends Component
{
    public $showProductModal = false;
    public $search = '';
    public $products;
    public $selectedProduct;
    public $productId;
    public $productName;
    public $productDesc;
    public $qty = 0;
    public $qtyType = 'stuks';
    public $pricePerUnit = 0.00;
    public $total = 0.00;
    public $quoteLines = [];
    public $freeText;
    public $selectedCustomer;

    protected $listeners = [
        'getTinyMceContent',
        'getFreeTextContent',
        'quoteCustomerSelected'
    ];

    public function mount()
    {
        $this->products = new Collection();
        $this->freeText = '<p>Bedankt voor uw interesse in onze producten.</p>
            <p>Met vriendelijke groet,</p>
            <p>'. Auth::user()->name . '</p>';
    }

    public function getTinyMceContent($value)
    {
        $this->productDesc = $value;
    }

    public function getFreeTextContent($value)
    {
        $this->freeText = $value;
        $this->dispatch('freeTextAltered');
    }

    public function updatedSearch()
    {
        $this->products = Product::where('name', 'like', '%'.$this->search.'%')->get();
    }

    public function selectProduct(Product $product)
    {
        $this->selectedProduct = $product;
        $this->productId = $product->id;
        $this->productName = $product->name;
        $this->dispatch('productModalOpened');
    }

    public function openProductModal()
    {
        $this->showProductModal = true;
        $this->dispatch('productModalOpened');
    }

    public function addProductLine()
    {
        $this->quoteLines[] = [
            'product_id' => $this->productId,
            'product_name' => $this->productName,
            'product_desc' => $this->productDesc,
            'price_per_unit' => (float) $this->pricePerUnit,
            'price' => (float)$this->pricePerUnit * $this->qty,
            'qty' => $this->qty,
            'qty_type' => $this->qtyType
        ];

        $this->total += (float)$this->pricePerUnit * $this->qty;

        $this->showProductModal = false;
        $this->dispatch('freeTextAltered');
        $this->resetFields();
    }

    public function resetFields()
    {
        $this->selectedProduct = null;
        $this->productName = '';
        $this->productId = null;
        $this->productDesc = '';
        $this->search = '';
        $this->qty = 0;
        $this->qtyType = 'stuks';
        $this->pricePerUnit = 0.00;
    }

    public function quoteCustomerSelected($value)
    {
        $this->selectedCustomer = $value;
    }

    public function saveQuote()
    {
        $quote = Quote::create([
            'customer_id' => $this->selectedCustomer['id'],
            'customer_email' => $this->selectedCustomer['email'],
            'customer_name' => $this->selectedCustomer['name'],
            'reply_to_mail' => '<EMAIL>',
            'total' => $this->total,
            'extra_info' => $this->freeText,
            'user_id' => Auth::id()
        ]);

        foreach ($this->quoteLines as $quoteLine) {
            $quote->quoteLines()->create([
                'product_id' => $quoteLine['product_id'],
                'name' => $quoteLine['product_name'],
                'qty' => $quoteLine['qty'],
                'qty_type' => $quoteLine['qty_type'],
                'line_desc' => $quoteLine['product_desc'],
                'price' => $quoteLine['price_per_unit']
            ]);
        }
    }

    public function render()
    {
        return view('livewire.create-quote');
    }
}
