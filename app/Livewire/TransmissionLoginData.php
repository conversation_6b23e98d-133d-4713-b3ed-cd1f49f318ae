<?php

namespace App\Livewire;

use App\Models\Settings;
use Livewire\Component;

class TransmissionLoginData extends Component
{
    public $username;
    public $password;

    public function mount()
    {
        $username = Settings::where('key', 'transmission_username')->first();
        $this->username = $username ? $username->value : null;

        $password = Settings::where('key', 'transmission_password')->first();
        $this->password = $password ? $password->value : null;
    }

    public function saveCredentials()
    {
        if ($this->username) {
            Settings::updateOrCreate(
                ['key' => 'transmission_username'],
                [
                    'key' => 'transmission_username',
                    'value' => $this->username
                ]
            );
        }

        if ($this->password) {
            Settings::updateOrCreate(
                ['key' => 'transmission_password'],
                [
                    'key' => 'transmission_password',
                    'value' => $this->password
                ]
            );
        }
        $this->dispatch('banner-message', ['style' => 'success', 'message' => 'Opgeslagen.']);
    }

    public function render()
    {
        return view('livewire.transmission-login-data');
    }
}
