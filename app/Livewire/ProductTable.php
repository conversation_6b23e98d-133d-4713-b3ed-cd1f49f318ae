<?php

namespace App\Livewire;

use App\Models\Product;
use Illuminate\Http\JsonResponse;
use Livewire\Component;
use Yajra\DataTables\Facades\DataTables;

class ProductTable extends Component
{
    public function render()
    {
        return view('livewire.product-table');
    }

    public function getProducts()
    {
        $query = Product::where('source', 'retail')->with('customerPrices');

        return DataTables::of($query)
            ->addColumn('pricelists', function ($product) {
            $pricelists = [];
                $customerPrices = $product->customerPrices;

                foreach ($customerPrices as $customerPrice) {
                    if ($customerPrice->pricelist) {
                        $pricelists[] = $customerPrice->pricelist->name;
                    }
                }
            return implode(', ', $pricelists);
            })
            ->addColumn('has_base_stock', function ($product) {
                return $product->baseStockProducts->isNotEmpty() ? 'Ja' : 'Nee';
            })
            ->addColumn('base_products', function ($product) {
                return $product->baseProducts->isNotEmpty() ? 'Ja' : 'Nee';
            })
            ->make(true);
    }
}
