<?php

namespace App\Livewire;

use App\Models\Quote;
use Livewire\Component;
use Yajra\DataTables\Facades\DataTables;

class QuotesTable extends Component
{
    public function getQuotes()
    {
        $query = Quote::with('customer', 'user')->orderBy('id', 'desc');

        return DataTables::of($query)->make(true);
    }

    public function render()
    {
        return view('livewire.quotes-table');
    }
}
