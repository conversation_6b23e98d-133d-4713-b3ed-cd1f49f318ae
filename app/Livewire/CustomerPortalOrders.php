<?php

namespace App\Livewire;

use App\Models\Order;
use Livewire\Component;
use Yajra\DataTables\Facades\DataTables;

class CustomerPortalOrders extends Component
{
    public function getOrders()
    {
        $query = Order::where('customer_id', auth()->user()->customer_id)->with('customer', 'shipment', 'address')->get();

        return DataTables::of($query)
            ->addColumn('hasShipment', function ($order) {
                return $order->hasShipment;
            })
            ->make(true);
    }

    public function newOrder()
    {
        return redirect()->route('customer.new.order');
    }

    public function render()
    {
        return view('livewire.customer-portal-orders');
    }
}
