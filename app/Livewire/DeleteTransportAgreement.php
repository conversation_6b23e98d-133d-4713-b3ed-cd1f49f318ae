<?php
declare(strict_types=1);

namespace App\Livewire;

use Livewire\Component;
use App\Models\TransportPriceAgreement;

class DeleteTransportAgreement extends Component
{
    public $transportPriceAgreementId;
    public $customerId;
    public bool $confirmingTransportAgreementDeletion = false;

    public function mount($transportPriceAgreementId, $customerId)
    {
        $this->transportPriceAgreementId = $transportPriceAgreementId;
        $this->customerId = $customerId;
    }

    public function confirmTransportAgreementDeletion()
    {
        $this->confirmingTransportAgreementDeletion = true;
    }

    public function deleteTransportAgreement()
    {
        $transportPriceAgreement = TransportPriceAgreement::find($this->transportPriceAgreementId);

        if ($transportPriceAgreement) {
            $transportPriceAgreement->delete();
        }

        $this->confirmingTransportAgreementDeletion = false;

        return redirect()->route('customer.edit', ['customer' => $this->customerId]);
    }

    public function render()
    {
        return view('livewire.delete-transport-agreement');
    }
}
