<?php

namespace App\Livewire;

use App\Models\Address;
use Illuminate\Database\Eloquent\Collection;
use Livewire\Component;

class EditOrderShippingAddress extends Component
{
    public $address;
    public $showModal = false;
    public $addresses;
    public $selectedCustomer;
    public $search = '';

    public $companyName;
    public $name;
    public $street;
    public $housenumber;
    public $postalCode;
    public $city;
    public $country;
    public $email;

    public $hasShipment;

    public function mount($address, $hasShipment)
    {
        $this->address = $address;
        $this->selectedCustomer = $address['customer_id'];
        $this->addresses = new Collection();
        $this->hasShipment = $hasShipment;

        $this->companyName = $address->company_name;
        $this->name = $address->name;
        $this->street = $address->street;
        $this->housenumber = $address->housenumber;
        $this->postalCode = $address->postal_code;
        $this->city = $address->city;
        $this->country = $address->country;
        $this->email = $address->email;
    }

    public function updatedSearch()
    {
        $this->addresses = Address::where('customer_id', $this->selectedCustomer)
            ->where('company_name', 'like', '%'.$this->search.'%')->get();
    }

    public function selectAddress(Address $address)
    {
        $this->address = $address;
        $this->search = '';
        $this->dispatch('addressChanged', $address);
        $this->addresses = new Collection();
        $this->showModal = false;
    }

    public function saveAddress()
    {
        $this->address->company_name = $this->companyName;
        $this->address->name = $this->name;
        $this->address->street = $this->street;
        $this->address->housenumber = $this->housenumber;
        $this->address->postal_code = $this->postalCode;
        $this->address->city = $this->city;
        $this->address->country = $this->country;
        $this->address->email = $this->email;
        $this->address->save();

        $this->dispatch('addressChanged', $this->address);
        $this->showModal = false;
    }

    public function render()
    {
        return view('livewire.edit-order-shipping-address');
    }
}
