<?php

namespace App\Livewire;

use App\Models\Product;
use App\Models\SKU;
use Livewire\Component;

class Woocommerce extends Component
{
    public $product;
    public $sku;
    public $qty;

    protected $rules = [
        'sku' => 'required|unique:skus,sku',
    ];

    public function mount($productId)
    {
        $this->product = Product::find($productId);
    }

    public function addSku()
    {
        $this->validate();

        $this->product->skus()->create(['sku' => $this->sku, 'qty' => $this->qty]);

        $this->product->load('skus');

        $this->sku = null;
        $this->qty = null;
    }

    public function removeSku($skuId)
    {
        $sku = SKU::find($skuId);
        $sku->delete();

        $this->product->load('skus');
    }

    public function render()
    {
        return view('livewire.woocommerce');
    }
}
