<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\OrderComment;
use Illuminate\Support\Facades\Auth;

class OrderComments extends Component
{
    public $order;
    public $comment;
    public $showForm = false;

    protected $rules = [
        'comment' => 'required|max:255',
    ];

    public function render()
    {
        $comments = OrderComment::where('order_id', $this->order->id)
            ->with('user') // If you want to display user information
            ->orderByDesc('created_at')
            ->get();

        return view('livewire.order-comments', compact('comments'));
    }

    public function addComment()
    {
        $this->showForm = true;
    }

    public function saveComment()
    {
        $this->validate();

        OrderComment::create([
            'order_id' => $this->order->id,
            'user_id' => Auth::id(),
            'comment' => $this->comment,
        ]);

        $this->comment = '';
        $this->showForm = false;
    }
}

