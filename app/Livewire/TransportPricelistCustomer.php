<?php

namespace App\Livewire;

use App\Models\Customer;
use App\Models\Pricelist;
use App\Models\TransportPricelist;
use Illuminate\Database\Eloquent\Collection;
use Livewire\Component;

class TransportPricelistCustomer extends Component
{
    public $pricelistid;
    public $search = '';
    public $customers;
    public $attachedCustomers;
    public $pricelist;
    public $errorMessage;

    public function mount()
    {
        $this->customers = new Collection();
        $this->pricelist = TransportPricelist::find($this->pricelistid);
        $this->getPricelistCustomers();
    }

    public function getPricelistCustomers()
    {
        $this->attachedCustomers = $this->pricelistid ?
            Customer::whereHas('transportpricelists', function ($query) {
                $query->where('transport_pricelists.id', $this->pricelistid);
            })->get() :
            new Collection();
    }

    public function updatedSearch()
    {
        if ($this->search == '') {
            $this->customers = new Collection();
        } else {
            $this->customers = Customer::where('name', 'like', '%'.$this->search.'%')->get();
        }
    }

    public function selectCustomer(Customer $customer)
    {
        $existingPricelists = $customer->transportpricelists;
        $this->customers = new Collection();
        $this->search = '';

        foreach ($existingPricelists as $existingPricelist) {
            if ($existingPricelist->from <= $this->pricelist->to && $this->pricelist->from <= $existingPricelist->to) {
                $this->errorMessage = 'Deze klant kan niet worden gekoppeld. De klant is gekoppeld aan ' . $existingPricelist->name . '. En deze data overlappen.';

                return;
            }
        }

        $customer->transportpricelists()->attach($this->pricelistid);
        $this->getPricelistCustomers();
    }

    public function removeCustomer(Customer $customer)
    {
        $customer->transportpricelists()->detach($this->pricelistid);
        $this->getPricelistCustomers();
    }

    public function render()
    {
        return view('livewire.transport-pricelist-customer');
    }
}
