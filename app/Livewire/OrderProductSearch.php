<?php

namespace App\Livewire;

use App\Models\Customer;
use App\Models\Product;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Livewire\Component;

class OrderProductSearch extends Component
{
    protected $listeners = [
        'customerSelected' => 'handleCustomerSelected',
        'shippingAddress' => 'handleShippingAddress',
        'orderDate' => 'handleOrderDate'
    ];

    public $showModal = false;
    public $showTransportModal = false;
    public $search = '';
    public $products;
    public $selectedProduct;
    public $storageProduct;
    public $quantity;
    public $orderLines = [];
    public $selectedCustomer;
    public $postalCode;
    public $orderDate;
    public $transportPrice;
    public $transportCostPrice;
    public $shipperSelected = false;
    public $errorMessage;

    public function mount()
    {
        $this->products = new Collection();
        $this->orderDate = Carbon::now()->format('Y-m-d');
    }

    public function handleOrderDate($date)
    {
        $this->orderDate = $date;
    }

    public function handleCustomerSelected($customer)
    {
        $this->selectedCustomer = $customer;
    }

    public function handleShippingAddress($address)
    {
        if (is_array($address)) {
            $this->postalCode = $address['postal_code'];
        } else {
            $this->transportPrice = 0.00;
            $this->transportCostPrice = 0.00;
            $this->dispatch('transportPrice', $this->transportPrice);
            $this->dispatch('transportCostPrice', $this->transportCostPrice);
            $this->shipperSelected = true;
            $this->postalCode = '9951SJ';
        }
    }

    public function updatedSearch()
    {
        $this->products = Product::where(function($query) {
            $query->where('name', 'like', '%' . $this->search . '%')
                ->orWhere('sku', 'like', '%' . $this->search . '%');
        })->get();
    }

    public function selectProduct(Product $product)
    {
        $this->selectedProduct = $product;
        if ($product->storageProduct) {
            $this->storageProduct = $product->storageProduct->toArray();
        }
    }

    public function addOrderLine()
    {
        try {
            if (!$this->selectedProduct->transmissionData) {
                throw new Exception('Transmission data niet ingevuld bij product');
            }
            $customer = Customer::find($this->selectedCustomer);
            $product_id = $this->selectedProduct->id;
            $this->dispatch('orderLog',
                '<li>Product geselecteerd: '.$this->selectedProduct->name.'. Aantal: '.$this->quantity.'</li>');
            // Is there an entry in a Pricelist for this customer?
            $priceList = $customer->load([
                'pricelists' => function ($query) {
                    $today = now()->toDateString();
                    $query->whereDate('from', '<=', $today)
                        ->whereDate('to', '>=', $today);
                },
                'pricelists.priceTiers' => function ($query) use ($product_id) {
                    $query->where('product_id', $product_id)
                        ->where('from_qty', '<=', $this->quantity)
                        ->where('to_qty', '>=', $this->quantity)
                        ->where('type', 'D');
                }
            ]);
            $priceList = $priceList->pricelists->first();
            $price = 0.00;
            $calculation = '';
            $freeShipping = 0;
            if ($priceList) {
                $priceTier = $priceList->priceTiers->first();
                if ($priceTier) {
                    $price = $priceTier->price;
                    $freeShipping = $priceTier->free_shipping;
                    $calculation = 'Toegepaste prijsstaffel: Van '.$priceTier->from_qty.' tot '.$priceTier->to_qty.': €'.$priceTier->price.' Vanuit Prijslijst: '.$priceList->name;
                } else {
                    [$price, $calculation] = $this->normalPriceCalculation();
                }
            } else {
                // Is there a price on product level for this specific customer?
                [$price, $calculation] = $this->normalPriceCalculation();
            }

            $this->dispatch('orderLog', '<li>'.$calculation.'</li>');
            $this->selectedProduct->load('transmissionData');

            $transportLength = 0;
            if (!$this->selectedProduct->is_by_product) {
                $transportLength = $this->selectedProduct->transportUnit[$this->selectedProduct->transmissionData->unit_type];
            }

            $this->orderLines[] = [
                'id' => $this->selectedProduct->id,
                'product' => $this->selectedProduct,
                'transport_length' => $transportLength,
                'qty' => $this->quantity,
                'price' => $this->quantity * $price,
                'price_calculation' => $calculation,
                'storage_product' => $this->storageProduct,
                'free_shipping' => $freeShipping
            ];

            $this->dispatch('orderLines', $this->orderLines);

            $this->selectedProduct = null;
            $this->quantity = null;
            $this->search = '';
            $this->products = new Collection();
            $this->showModal = false;
            $this->errorMessage = NULL;
        } catch(Exception $e) {
            $this->errorMessage = $e->getMessage();
        }
    }

    public function removeOrderline($index) {
        unset($this->orderLines[$index]);
        $this->dispatch('orderLines', $this->orderLines);
    }

    public function addTransportPrice()
    {
        $this->dispatch('transportPrice', $this->transportPrice);
        $this->dispatch('transportCostPrice', $this->transportCostPrice);
        $this->shipperSelected = true;
        $this->showTransportModal = false;
    }

    public function addTransmissionTransport()
    {
        $this->shipperSelected = true;
        $this->dispatch('transmissionTransport');
    }

    public function render()
    {
        return view('livewire.order-product-search');
    }

    protected function normalPriceCalculation(): array
    {
        $this->selectedProduct->load([
            'customerPrices' => function ($query) {
                $orderDate = $this->orderDate;
                $query->where('customer_id', $this->selectedCustomer)
                    ->where('from_qty', '<=', $this->quantity)
                    ->where('to_qty', '>=', $this->quantity)
                    ->where(function ($query) use ($orderDate) {
                        $query->where(function ($query) use ($orderDate) {
                            $query->whereDate('from', '<=', $orderDate)
                                ->whereDate('to', '>=', $orderDate);
                        })->orWhereNull('from')
                            ->orWhereNull('to');
                    });
            }
        ]);

        if ($tierPrice = $this->selectedProduct->customerPrices->first()) {
            $price = $tierPrice->price;
            $calculation = 'Toegepaste prijsstaffel: Van '.$tierPrice->from_qty.' tot '.$tierPrice->to_qty.': €'.$tierPrice->price;
        } else {
            $price = $this->selectedProduct->price;
            $calculation = 'Geen prijsstaffel voor dit aantal bij dit product.';
        }

        return [$price, $calculation];
    }
}
