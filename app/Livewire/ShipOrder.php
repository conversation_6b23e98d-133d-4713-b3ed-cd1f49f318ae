<?php

namespace App\Livewire;

use App\Models\Order;
use App\Models\Picklist;
use App\Models\Settings;
use App\Models\Shipment;
use Carbon\Carbon;
use Carbon\CarbonInterface;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Livewire\Component;

class ShipOrder extends Component
{
    public $order;
    public $showLabel = false;
    public $zplImageUrl;
    public $shipmentStatus;
    public $showShipmentStatusModal = false;
    public $isShipped = false;
    public $instruction;
    public $exchangeUnit = false;

    public function mount($order)
    {
        $this->order = Order::with('orderitems.product.transmissionData', 'address', 'shipment')->find($order);
    }

    /**
     * Register shipment with Transmission. Add Shipment and add Picklist.
     * Picklist is delivery date minus one day. Pick the day before delivery.
     *
     * @return void
     */
    public function shipOrder()
    {
        $this->isShipped = true;
        try {
            $deliveryDate = Carbon::parse($this->order->delivery_date);

            if ($deliveryDate->lt(Carbon::today())) {
                $this->dispatch('banner-message',
                    ['style' => 'danger', 'message' => 'Leverdatum kan niet in het verleden liggen.']);

                return;
            }

            $pickDate = $deliveryDate->subDay();

            if ($pickDate->isWeekend()) {
                $pickDate->previous(CarbonInterface::FRIDAY);
            }

            $token = $this->transmissionLogin();
            $transmissionUnits = [];
            $picklistItems = [];
            $count = 1;

            foreach ($this->order->orderitems as $orderitem) {
                $transportUnitQty = $orderitem->qty;
                $product = $orderitem->product;
                if ($product->transport_qty) {
                    $transportUnitQty = $orderitem->qty / $product->transport_qty;
                }

                for ($i = 0; $i < $transportUnitQty; $i++) {
                    $transmissionUnits[] = [
                        'unit_number' => (string) $count,
                        'unit_type' => $orderitem->product->transmissionData->unit_type,
                        'description' => $orderitem->product->transmissionData->description,
                        'exchange_unit' => (int) $this->exchangeUnit,
                        'measurements' => [
                            'weight' => $orderitem->product->transmissionData->transport_weight,
                            'length' => $orderitem->product->transmissionData->length,
                            'width' => $orderitem->product->transmissionData->width,
                            'height' => $orderitem->product->transmissionData->height
                        ]
                    ];
                    $count++;
                }
                $picklistItems[] = [
                    'product_id' => $orderitem->product->id,
                    'order_item_id' => $orderitem->id,
                    'sku' => $orderitem->product->sku,
                    'name' => $orderitem->product_name,
                    'qty' => $transportUnitQty,
                    'pick_date' => $pickDate->format('Y-m-d'),
                    'customer' => $this->order->customer->id
                ];
            }
            $body = [
                'type' => 'T',
                'portal' => 'new',
                'depot_number' => '9800',
                'customer_number' => "130485",
                'date' => $deliveryDate->format('Y-m-d'),
                'addresses' => [
                    [
                        'type' => 'consignor',
                        'name' => 'Pelfin',
                        'address1' => 'Het Aanleg',
                        'housenumber' => '21',
                        'postalcode' => '9951SJ',
                        'city' => 'Winsum',
                        'country_code' => 'NL'
                    ],
                    [
                        'type' => 'delivery',
                        'name' => $this->order->address->name,
                        'address1' => $this->order->address->street,
                        'housenumber' => $this->order->address->housenumber,
                        'postalcode' => str_replace(' ', '', $this->order->address->postal_code),
                        'city' => $this->order->address->city,
                        'country_code' => $this->order->address->country,
                        'contact' => [
                            'email_address' => $this->order->address->email,
                            'phonenumber' => $this->order->address->telephone
                        ]
                    ]
                ],
                'shipment_units' => $transmissionUnits,
                "labels" => "ZPL"
            ];
            if ($this->instruction != '') {
                $body['text_messages'][] = [
                    'type' => 'AFLINFO',
                    'remarks' => $this->instruction
                ];
                $logEntry = $this->order->orderLog()->latest()->first();
                $existingLog = $logEntry->log;
                $newLogData = '<li>Afleverinfo meegezonden: '.$this->instruction.'</li>';

                $updatedLog = $existingLog.$newLogData;

                $logEntry->update([
                    'log' => $updatedLog
                ]);
            }
            $body['references'][] = [
                'type' => 'NRORDER',
                'reference' => strtoupper(substr($this->order->customer->name, 0, 4)).'-'.$this->order->id
            ];

            $response = Http::withHeaders([
                'Authorization' => 'Bearer '.$token,
                'Content-Type' => 'application/json',
            ])->post('https://api.trans-mission.nl/api/shipments/shipment', $body);

            $response = $response->json();
        } catch (\Exception $e) {
            Log::error($e->getMessage());
        }
        if ($response['status'] === 200) {
            Shipment::create([
                'order_id' => $this->order->id,
                'delivery_date' => $this->order->delivery_date,
                'label' => $response['data']['labels']['label_content'],
                'track_trace' => $response['data']['tracking_url'],
                'shipment_number' => $response['data']['transport_number']
            ]);

            foreach ($picklistItems as $picklistItem) {
                $item = Picklist::where('order_item_id', $picklistItem['order_item_id'])->first();
                if (!$item) {
                    Picklist::create($picklistItem);
                } else {
                    $this->dispatch('banner-message',
                        ['style' => 'danger', 'message' => 'Er is al een picklist item voor deze orderregel.']);
                }
            }
            $this->dispatch('banner-message', [
                'style' => 'success',
                'message' => 'Aangemeld bij Transmission. Picklist datum: '.$pickDate->format('d-m-Y')
            ]);

            $this->order->status = Order::STATUS_READY_FOR_SHIPPING;
            $this->order->save();
        } else {
            Log::error($response);
            $errors = '';
            foreach ($response['meta']['error_list'] as $error) {
                $errors .= $error[1].' ';
            }
            $this->dispatch('banner-message',
                ['style' => 'danger', 'message' => 'Fout bij aanmelden bij Transmission. '.$errors]);
        }
    }


    public function getShipmentStatus()
    {
        try {
            $token = $this->transmissionLogin();
            $response = Http::withHeaders([
                'Authorization' => 'Bearer '.$token,
                'Content-Type' => 'application/json',
            ])->get('https://api.trans-mission.nl/api/shipments/shipment_status/transport_number/'.$this->order->shipment->shipment_number);
            $response = $response->json();
            $this->shipmentStatus = $response['data'];
            $this->showShipmentStatusModal = true;
        } catch (\Exception $e) {
            $this->dispatch('banner-message', ['style' => 'danger', 'message' => 'Geen status beschikbaar.']);
        }
    }


    public function transmissionLogin()
    {
        $expiresAt = Cache::get('api_token_expires_at');
        $now = now();

        if (Cache::get('api_token') && $now->lessThan($expiresAt)) {
            return Cache::get('api_token');
        }

        $username = Settings::where('key', 'transmission_username')->first();
        $password = Settings::where('key', 'transmission_password')->first();

        $response = Http::asForm()->post('https://api.trans-mission.nl/api/login', [
            'user' => $username->value,
            'password' => $password->value
        ]);

        // Check for a successful response
        if ($response['status'] == 200 && $response['result_code'] == 'ok') {
            $token = $response['access_token'];
            $expiresIn = $response['expires_in'];

            $expiresAt = $now->addSeconds($expiresIn);

            Cache::put('api_token', $token);
            Cache::put('api_token_expires_at', $expiresAt);

            return $token;
        }

    }

    public function printLabel()
    {
        try {
            $fp=pfsockopen("***************",9100);
            fputs($fp,base64_decode($this->order->shipment->label));
            fclose($fp);
        } catch (Exception $e) {
            echo '{"status": "error", "message": "' . $e->getMessage() . '"}';
        }
    }

    public function render()
    {
        return view('livewire.ship-order');
    }
}
