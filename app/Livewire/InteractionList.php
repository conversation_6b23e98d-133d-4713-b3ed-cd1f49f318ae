<?php

namespace App\Livewire;

use App\Models\Customer;
use App\Models\Interaction;
use Carbon\Carbon;
use Livewire\Component;
use Illuminate\Support\Facades\Auth;

class InteractionList extends Component
{
    public $customer_id;
    public $start_date;
    public $end_date;
    public $interactions = [];

    public $showModal = false;
    public $interactionDate;
    public $interactionType;
    public $interactionDescription;

    public function mount($customer_id)
    {
        $this->customer_id = $customer_id;
        $this->start_date = Carbon::now()->subWeeks(2)->startOfDay()->toDateString();
        $this->end_date = Carbon::now()->endOfDay()->toDateString();
        $this->loadInteractions();
    }
    public function openModal()
    {
        $this->showModal = true;
    }

    public function closeModal()
    {
        $this->showModal = false;
    }

    public function updated($field)
    {
        $this->validateOnly($field, [
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
        ]);
    }

    public function loadInteractions()
    {
        $this->validate([
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
        ]);

        $start = Carbon::parse($this->start_date)->startOfDay();
        $end = Carbon::parse($this->end_date)->endOfDay();

        $customer = Customer::find($this->customer_id);
        $this->interactions = $customer->interactions()
            ->with('user')
            ->whereBetween('created_at', [$start, $end])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    public function createInteraction()
    {
        $this->validate([
            'interactionType' => 'required',
            'interactionDescription' => 'required',
        ]);

        Interaction::create([
            'user_id' => Auth::id(),
            'type' => $this->interactionType,
            'content' => $this->interactionDescription,
            'customer_id' => $this->customer_id,
        ]);

        $this->interactionDate = '';
        $this->interactionType = '';
        $this->interactionDescription = '';

        $this->showModal = false;
        $this->loadInteractions();
    }

    public function render()
    {
        return view('livewire.interaction-list');
    }
}
