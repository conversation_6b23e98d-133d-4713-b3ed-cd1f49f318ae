<?php

namespace App\Livewire;

use Livewire\Component;
use <PERSON><PERSON>\Sanctum\PersonalAccessToken;

class UserApiToken extends Component
{
    public $token;


    public function createNewToken()
    {
        $newToken = auth()->user()->createToken('plefin-token');
        $this->token = $newToken->plainTextToken;
    }

    public function render()
    {
        return view('livewire.user-api-token');
    }
}
