<?php

namespace App\Livewire;

use App\Models\Order;
use Livewire\Component;
use Ya<PERSON>ra\DataTables\Facades\DataTables;

class OrderUnsent extends Component
{
    public function render()
    {
        return view('livewire.order-unsent');
    }

    public function getOrders()
    {
        $query = Order::with('customer', 'shipment', 'address')
            ->whereNotIn('status', ['COMPLETED', 'CANCELLED', 'RETURNED'])
            ->whereDoesntHave('shipment');

        return DataTables::of($query)
            ->addColumn('hasShipment', function ($order) {
                if ($order->shipper === 'pickup') {
                    return true;
                }
                return $order->hasShipment;
            })
            ->make(true);
    }
}
