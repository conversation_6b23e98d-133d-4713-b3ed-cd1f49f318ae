<?php

namespace App\Livewire;

use App\Models\Product;
use App\Models\StockPlace;
use Livewire\Component;

class StockPlaces extends Component
{
    public $product;
    public $showModal = false;

    public $selectedStockPlace;
    public $name;
    public $qty;
    public $cost;

    protected $listeners = ['updated' => '$refresh'];

    public function mount($productId): void
    {
        $this->product = Product::find($productId);
    }

    public function cancelModal(): void
    {
        $this->showModal = false;
        $this->resetFields();
    }

    public function saveStockPlace(): void
    {

        if ($this->selectedStockPlace) {
            $this->selectedStockPlace->name = $this->name;
            $this->selectedStockPlace->qty = $this->qty;
            $this->selectedStockPlace->cost = $this->cost;
            $this->selectedStockPlace->save();
        } else {
            $this->product->stockPlaces()->create([
                'name' => $this->name,
                'qty' => $this->qty,
                'cost' => $this->cost,
                'position' => $this->product->stockPlaces()->count() + 1
            ]);
        }

        $this->dispatch('updated');
        $this->resetFields();
        $this->showModal = false;
    }

    public function removeStockPlace(StockPlace $stockPlace): void
    {
        $stockPlace->delete();
        $this->dispatch('updated');
    }

    public function editStockPlace(StockPlace $stockPlace): void
    {
        $this->selectedStockPlace = $stockPlace;
        $this->name = $stockPlace->name;
        $this->qty = $stockPlace->qty;
        $this->cost = $stockPlace->cost;
        $this->showModal = true;
    }

    private function resetFields()
    {
        $this->name = NULL;
        $this->qty = NULL;
        $this->cost = NULL;
        $this->selectedStockPlace = NULL;
    }

    public function movePositionUp(StockPlace $stockPlace)
    {
        $oldPosition = $stockPlace->position;
        $newPosition = $stockPlace->position - 1;
        $currentStockPlace = $this->product->stockPlaces->where('position', $newPosition)->first();
        $currentStockPlace->position = $oldPosition;
        $currentStockPlace->save();

        $stockPlace->position = $newPosition;
        $stockPlace->save();

        $this->dispatch('updated');
    }

    public function movePositionDown(StockPlace $stockPlace)
    {
        $oldPosition = $stockPlace->position;
        $newPosition = $stockPlace->position + 1;
        $currentStockPlace = $this->product->stockPlaces->where('position', $newPosition)->first();
        $currentStockPlace->position = $oldPosition;
        $currentStockPlace->save();

        $stockPlace->position = $newPosition;
        $stockPlace->save();

        $this->dispatch('updated');
    }

    public function render()
    {
        return view('livewire.stock-places');
    }
}
