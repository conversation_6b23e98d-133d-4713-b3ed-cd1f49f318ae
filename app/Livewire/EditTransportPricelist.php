<?php

namespace App\Livewire;

use App\Models\TransportPricelist;
use Carbon\Carbon;
use Livewire\Component;

class EditTransportPricelist extends Component
{
    public $priceList;
    public $from;
    public $to;
    public $showModal = false;
    public $showEditModal = false;
    public $trailerSpot;
    public $priceNL;
    public $priceBE;
    public $priceZV;
    public $priceWE;
    public $tierId;

    public function mount($pricelistId)
    {
        $this->priceList = TransportPricelist::with(['transportPriceTiers' => function($query) {
            $query->orderBy('trailer_spot', 'asc');
        }])->find($pricelistId);
        $this->from = Carbon::parse($this->priceList->from)->format('Y-m-d');
        $this->to = Carbon::parse($this->priceList->to)->format('Y-m-d');
    }

    public function updatedFrom()
    {
        $this->priceList->from = $this->from;
        $this->priceList->save();
    }

    public function updatedTo()
    {
        $this->priceList->to = $this->to;
        $this->priceList->save();
    }

    public function createTier()
    {
        $this->priceList->transportPriceTiers()->create([
            'trailer_spot' => $this->trailerSpot,
            'price_nl' => $this->priceNL,
            'price_be' => $this->priceBE,
            'price_zv' => $this->priceZV,
            'price_we' => $this->priceWE,
        ]);
        $this->resetFields();
        $this->refreshTiers();
    }

    public function openEditModal($tierId)
    {
        $this->tierId = $tierId;
        $transportPriceTier = $this->priceList->transportPriceTiers()->find($tierId);
        $this->trailerSpot = $transportPriceTier->trailer_spot;
        $this->priceNL = $transportPriceTier->price_nl;
        $this->priceBE = $transportPriceTier->price_be;
        $this->priceZV = $transportPriceTier->price_zv;
        $this->priceWE = $transportPriceTier->price_we;
        $this->showEditModal = true;
    }

    public function editTier()
    {
        $this->priceList->transportPriceTiers()->find($this->tierId)->update([
            'trailer_spot' => $this->trailerSpot,
            'price_nl' => $this->priceNL,
            'price_be' => $this->priceBE,
            'price_zv' => $this->priceZV,
            'price_we' => $this->priceWE,
        ]);
        $this->resetFields();
        $this->refreshTiers();
        $this->showEditModal = false;
    }

    public function deleteTier($tierId)
    {
        $transportPriceTier = $this->priceList->transportPriceTiers()->find($tierId);
        $transportPriceTier->delete();
        $this->refreshTiers();
    }

    public function resetFields()
    {
        $this->tierId = null;
        $this->trailerSpot = null;
        $this->priceNL = null;
        $this->priceBE = null;
        $this->priceZV = null;
        $this->priceWE = null;
        $this->showModal = false;
    }

    private function refreshTiers()
    {
        $this->priceList->load(['transportPriceTiers' => function($query) {
            $query->orderBy('trailer_spot', 'asc');
        }]);
    }

    public function render()
    {
        return view('livewire.edit-transport-pricelist');
    }
}
