<?php

namespace App\Livewire;

use Livewire\Component;

class EditOrderDeliveryDate extends Component
{
    public $deliveryDate;
    public $hasShipment;
    public $status;

    public function mount($deliveryDate, $hasShipment, $status)
    {
        $this->deliveryDate =  optional($deliveryDate)->format('Y-m-d');
        $this->hasShipment = $hasShipment;
        $this->status = $status;
    }

    public function updatedDeliveryDate()
    {
        $this->dispatch('deliveryDate', $this->deliveryDate);
    }

    public function render()
    {
        return view('livewire.edit-order-delivery-date');
    }
}
