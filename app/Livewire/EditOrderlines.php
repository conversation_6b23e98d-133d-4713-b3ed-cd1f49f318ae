<?php

namespace App\Livewire;

use App\Models\Order;
use App\Models\OrderItem;
use Livewire\Component;

class EditOrderlines extends Component
{
    public $order;
    public $removedOrderlines = [];
    public $editingOrderlineId;
    public $qty;
    public $price_ex_vat;
    public $name;
    public $sku;

    protected $listeners = [
        'orderSaved' => 'handleOrderSaved',
        'orderDate' => 'handleOrderDateChanged'
    ];

    protected $rules = [
        'qty' => 'required|integer|min:0',
        'price_ex_vat' => 'required|numeric|min:0',
    ];

    public function mount($order)
    {
        $this->order = Order::with('orderitems')->find($order);
    }

    public function handleOrderSaved()
    {
        $this->order->load('orderitems');
    }

    public function handleOrderDateChanged($orderDate)
    {
        $orderlinesChanges = [];
        foreach ($this->order->orderitems as $orderitem) {
            $orderitem->product->load(['customerPrices' => function ($query) use ($orderDate, $orderitem) {
                $query->where('customer_id', $this->order->customer_id)
                    ->where('from_qty', '<=', $orderitem->qty)
                    ->where('to_qty', '>=', $orderitem->qty)
                    ->where(function ($query) use ($orderDate) {
                        $query->where(function ($query) use ($orderDate) {
                            $query->where('from', '<=', $orderDate)
                                ->where('to', '>=', $orderDate);
                        })
                            ->orWhereNull('from')
                            ->orWhereNull('to');
                    });
            }]);
            $price = 0.00;
//            $calculation = '';
            if($tierPrice = $orderitem->product->customerPrices->first()) {
                $price = $tierPrice->price;
//                $calculation = 'Toegepaste prijsstaffel: Van ' . $tierPrice->from_qty . ' tot ' . $tierPrice->to_qty . ': €' . $tierPrice->price;
            } else {
                $price = $orderitem->product->price;
//                $calculation = 'Geen prijsstaffel voor dit aantal bij dit product.';
            }
            $orderitem->price_ex_vat = $price * $orderitem->qty;

            $orderlinesChanges[$orderitem->id] = [
                'id' => $orderitem->id,
                'qty' => $orderitem->qty,
                'price' => $orderitem->price_ex_vat,
                'cost' => $orderitem->cost
            ];
        }
        $this->dispatch('orderItemsChanged', $orderlinesChanges);
    }

    public function editOrderline($orderlineId)
    {
        $this->editingOrderlineId = $orderlineId;
        $orderline = OrderItem::find($orderlineId);

        // Now that you have the orderline, populate your properties.
        $this->qty = $orderline->qty;
        $this->price_ex_vat = $orderline->price_ex_vat;
        $this->name = $orderline->product_name;
        $this->sku = $orderline->sku;
    }

    public function updateOrderline()
    {
        $orderline = $this->order->orderitems->firstWhere('id', $this->editingOrderlineId);
        // If the quantity changes and not the price then we need to get the price from the product
        if ($orderline->qty != $this->qty && $orderline->price_ex_vat == $this->price_ex_vat) {
            $orderline->product->load(['customerPrices' => function ($query) {
                $query->where('customer_id', $this->order->customer_id)
                    ->where('from_qty', '<=', $this->qty)
                    ->where('to_qty', '>=', $this->qty);
            }]);
            $price = 0.00;
            $calculation = '';
            if($tierPrice = $orderline->product->customerPrices->first()) {
                $price = $tierPrice->price;
                $calculation = 'Toegepaste prijsstaffel: Van ' . $tierPrice->from_qty . ' tot ' . $tierPrice->to_qty . ': €' . $tierPrice->price;
            } else {
                $price = $this->selectedProduct->price;
                $calculation = 'Geen prijsstaffel voor dit aantal bij dit product.';
            }
            $orderline->price_ex_vat = $price * $this->qty;
            $orderline->cost = $orderline->product->cost * $this->qty;
        } else {
            $orderline->price_ex_vat = $this->price_ex_vat;
        }
        $orderline->qty = $this->qty;

        $orderlinesChanges = [
            'id' => $orderline->id,
            'qty' => $orderline->qty,
            'price' => $orderline->price_ex_vat,
            'cost' => $orderline->cost
        ];

        $this->dispatch('recalculateTransport');
        $this->dispatch('orderItemsChanged', $orderlinesChanges);

        $this->editingOrderlineId = null;
        $this->reset(['qty', 'price_ex_vat', 'name', 'sku']);
    }

    public function removeOrderitem($id)
    {
        $this->removedOrderlines[] = $id;

        $this->dispatch('removedOrderlines', $this->removedOrderlines);
        $this->dispatch('orderChanged', true);
    }

    public function restoreOrderitem($id)
    {
        if (($key = array_search($id, $this->removedOrderlines)) !== false) {
            unset($this->removedOrderlines[$key]);
        }
        $this->dispatch('removedOrderlines', $this->removedOrderlines);
    }

    public function render()
    {
        return view('livewire.edit-orderlines');
    }
}
