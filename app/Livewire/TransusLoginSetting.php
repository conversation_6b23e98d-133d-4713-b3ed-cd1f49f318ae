<?php

namespace App\Livewire;

use App\Models\Settings;
use Livewire\Component;

class TransusLoginSetting extends Component
{
    public $username;
    public $password;

    public function mount()
    {
        $username = Settings::where('key', 'transus_username')->first();
        $this->username = $username ? $username->value : null;

        $password = Settings::where('key', 'transus_password')->first();
        $this->password = $password ? $password->value : null;
    }

    public function saveCredentials()
    {
        if ($this->username) {
            Settings::updateOrCreate(
                ['key' => 'transus_username'],
                [
                    'key' => 'transus_username',
                    'value' => $this->username
                ]
            );
        }

        if ($this->password) {
            Settings::updateOrCreate(
                ['key' => 'transus_password'],
                [
                    'key' => 'transus_password',
                    'value' => $this->password
                ]
            );
        }
        $this->dispatch('banner-message', ['style' => 'success', 'message' => 'Opgeslagen.']);
    }

    public function render()
    {
        return view('livewire.transus-login-setting');
    }
}
