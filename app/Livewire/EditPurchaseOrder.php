<?php

namespace App\Livewire;

use App\Models\PurchaseOrder;
use App\Models\PurchaseOrderline;
use App\Models\StockPlace;
use Illuminate\Support\Facades\Storage;
use Livewire\Component;

class EditPurchaseOrder extends Component
{
    public $purchaseOrderId;
    public $purchaseOrder;
    public $selectedOrderline;
    public $showReceivedModal = false;
    public $qty = 0.00;
    public $transportCost = 0.00;
    public $showTransportModal = false;
    public $totalUnits;
    public $sending = false;

    protected $listeners = ['received' => '$refresh'];

    public function mount()
    {
        $this->purchaseOrder = PurchaseOrder::find($this->purchaseOrderId);
    }

    public function registerDelivery(PurchaseOrderline $purchaseOrderline)
    {
        $this->selectedOrderline = $purchaseOrderline;
        $this->qty = $purchaseOrderline->qty;
        $this->showReceivedModal = true;
    }

    public function received()
    {
        $this->selectedOrderline->qty_received = $this->qty;
        $this->selectedOrderline->save();

        $this->checkForComplete();
        $this->dispatch('received');
        $this->showReceivedModal = false;
        $this->qty = 0.00;
        $this->selectedOrderline = null;
    }

    public function saveTransportCost()
    {
        $this->purchaseOrder->transport_cost = $this->transportCost;
        $this->purchaseOrder->save();

        $totalReceivedStock = 0;
        foreach ($this->purchaseOrder->purchaseOrderlines as $purchaseOrderline) {
            $totalReceivedStock += $purchaseOrderline->qty_received;
        }

        $costPerUnit = $this->purchaseOrder->transport_cost / $totalReceivedStock;

        foreach ($this->purchaseOrder->purchaseOrderlines as $purchaseOrderline) {
            $purchaseOrderline->transport_cost = $costPerUnit;
            $purchaseOrderline->save();
        }

        $this->showTransportModal = false;
    }

    public function addToStock()
    {
        foreach($this->purchaseOrder->purchaseOrderlines as $purchaseOrderline) {
            if ($purchaseOrderline->stock_place_id) {
                $stockPlace = StockPlace::find($purchaseOrderline->stock_place_id);
                $stockPlace->qty += $this->qty_received;
                $stockPlace->save();
            } else {
                $stockPlaceCount = count(StockPlace::where('product_id', $purchaseOrderline->product_id)->get());
                $stockPlace = new StockPlace();
                $stockPlace->product_id = $purchaseOrderline->product_id;
                $stockPlace->name = 'Inkooporder: ' . $this->purchaseOrder->id;
                $stockPlace->qty = $purchaseOrderline->qty_received;
                $stockPlace->cost = $purchaseOrderline->purchase_price + $purchaseOrderline->transport_cost;
                $stockPlace->position = $stockPlaceCount + 1;
                $stockPlace->save();
            }
        }

        $this->purchaseOrder->status = 'COMPLETED';
        $this->purchaseOrder->save();
    }

    public function checkForComplete()
    {
        foreach ($this->purchaseOrder->purchaseOrderLines as $line) {
            if (empty($line->qty_received)) {

                return;
            }
        }

        $this->purchaseOrder->status = 'RECEIVED';
        $this->purchaseOrder->save();
    }

    /**
     * Create a CSV for Planbord and save it to the RDS through FTP
     *
     * @return void
     */
    public function sendToPlanbord()
    {
        $this->sending = true;
        $csvContent = '';

        foreach($this->purchaseOrder->purchaseOrderlines as $purchaseOrderline) {
            $lineData = [
                $this->purchaseOrder->customer->exact_id, //abd_crednr
                $this->purchaseOrder->customer->name, //abd_laadnaam
                $this->purchaseOrder->customer->address, //abd_laadadres
                $this->purchaseOrder->customer->postal_code . ' ' . $this->purchaseOrder->customer->city, //abd_laadplaats
                '', //abd_telefoon
                '', //abd_laaddatum d-m-y 00:00
                '', //abd_aantal
                '', //abd_eenheid
                0, //abd_prijs
                $purchaseOrderline->product->sku, //abd_productnr
                $purchaseOrderline->product->name, //abd_productoms
                21, //abd_BTWperc
                '', //abd_instructie
                '', //vrg_productnr
                '', //vrg_productoms
                750600,
                'LOGI.SPAN ENERGY PRODUCTS B.V.', //vrg_losnaam
                'HET AANLEG 21', //vrg_losadres
                '9951 SJ WINSUM', //vrg_losplaats
                '0595-435660', //vrg_telefoon
                '', //vrg_lostijd
                '', //vrg_losdatum d-m-y 00:00
                $purchaseOrderline->qty, //vrg_aantal
                0.01, //vrg_prijs
                '', //vrg_eenheid
                '', //vrg_instructie
            ];

            // Manually format each row for the CSV
            $escapedLineData = array_map(function ($field) {
                // Escape only if the field contains the delimiter
                if (str_contains($field, ';')) {
                    $field = '"' . str_replace('"', '""', $field) . '"';
                }
                return $field;
            }, $lineData);

            // Create a string from the row with a semicolon delimiter
            $csvContent .= implode(';', $escapedLineData) . "\n";
        }
//        Storage::disk('ftp')->put('Retail/purchase_order.csv', $csvContent);
        Storage::disk('local')->put('purchase_order.csv', $csvContent);
        $this->dispatch('banner-message', ['style' => 'success', 'message' => 'Verzonden naar Planbord.']);
        $this->sending = false;
    }

    public function render()
    {
        return view('livewire.edit-purchase-order');
    }
}
