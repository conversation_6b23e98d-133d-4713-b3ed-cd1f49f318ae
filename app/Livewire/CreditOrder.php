<?php

namespace App\Livewire;

use App\Models\CreditOrder as CreditOrderModel;
use App\Models\Order;
use App\Models\OrderItem;
use Livewire\Component;

class CreditOrder extends Component
{
    public $order;
    public $selectedOrderline;
    public $qty;
    public $price;
    public $showEditOrderlineModal = false;
    public $creditOrderlines = [];
    public $reason;
    public $totalCreditAmount;

    public function mount($orderid)
    {
        $this->order = Order::find($orderid);
    }

    public function creditOrderline(OrderItem $orderItem)
    {
        $this->selectedOrderline = $orderItem;
        $this->qty = $orderItem->qty;
        $this->price = $orderItem->price_ex_vat;
        $this->showEditOrderlineModal = true;
    }

    public function cancelCreditOrderline()
    {
        $this->showEditOrderlineModal = false;
        $this->resetFields();
    }

    public function saveCreditOrderline()
    {
        $creditAmount = $this->selectedOrderline->price_ex_vat - $this->price;
        $this->creditOrderlines[$this->selectedOrderline->id] = [
            'qty' => $this->qty,
            'price' => $this->price,
            'credit_amount' => $creditAmount
        ];
        $this->totalCreditAmount += $creditAmount;
        $this->resetFields();
        $this->showEditOrderlineModal = false;
    }

    public function resetFields()
    {
        $this->selectedOrderline = NULL;
        $this->qty = NULL;
        $this->price = NULL;
    }

    public function saveCredit()
    {
        if (!$this->reason) {
            $this->dispatch('banner-message', ['style' => 'danger', 'message' => 'Reden is verplicht.']);

            return;
        }

        if (empty($this->creditOrderlines)) {
            $this->dispatch('banner-message', ['style' => 'danger', 'message' => 'Niks te crediteren.']);

            return;
        }

        $creditOrder = CreditOrderModel::create([
            'order_id' => $this->order->id,
            'credit_reason' => $this->reason,
            'credit_amount' => $this->totalCreditAmount
        ]);

        foreach($this->creditOrderlines as $index => $creditOrderline) {
            $creditOrder->creditLines()->create([
                'order_item_id' => $index,
                'qty' => $creditOrderline['qty'],
                'credit_amount' => $creditOrderline['credit_amount']
            ]);
        }

        $this->dispatch('banner-message', ['style' => 'success', 'message' => 'Credit aangemaakt.']);
    }

    public function render()
    {
        return view('livewire.credit-order');
    }
}
