<?php

namespace App\Livewire;

use Carbon\Carbon;
use Livewire\Component;

class EditOrderDate extends Component
{
    public $orderDate;
    public $hasShipment;
    public $status;

    public function mount($orderDate, $hasShipment, $status)
    {
        $this->orderDate = Carbon::parse($orderDate)->format('Y-m-d');
        $this->hasShipment = $hasShipment;
        $this->status = $status;
    }

    public function updatedOrderDate()
    {
        $this->dispatch('orderDate', $this->orderDate);
    }

    public function render()
    {
        return view('livewire.edit-order-date');
    }
}
