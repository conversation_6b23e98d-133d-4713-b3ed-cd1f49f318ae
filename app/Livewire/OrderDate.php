<?php

namespace App\Livewire;

use Carbon\Carbon;
use Livewire\Component;

class OrderDate extends Component
{
    public $orderDate;

    public function mount()
    {
        $this->orderDate = Carbon::now()->format('Y-m-d');
    }

    public function updatedOrderDate()
    {
        $this->dispatch('orderDate', $this->orderDate);
    }

    public function render()
    {
        return view('livewire.order-date');
    }
}
