<?php

namespace App\Livewire;

use App\Models\Settings;
use Livewire\Component;

class EuroLogFtpLoginSetting extends Component
{
    public String $username;
    public String $password;
    public String $ftp;

    public function mount()
    {
        $ftp = Settings::where('key', 'eurolog_ftp')->first();
        $this->ftp = $ftp ? $ftp->value : null;

        $username = Settings::where('key', 'eurolog_username')->first();
        $this->username = $username ? $username->value : null;

        $password = Settings::where('key', 'eurolog_password')->first();
        $this->password = $password ? $password->value : null;
    }

    public function saveCredentials()
    {
        if ($this->username) {
            Settings::updateOrCreate(
                ['key' => 'eurolog_ftp'],
                [
                    'key' => 'eurolog_ftp',
                    'value' => $this->ftp
                ]
            );
        }

        if ($this->username) {
            Settings::updateOrCreate(
                ['key' => 'eurolog_username'],
                [
                    'key' => 'eurolog_username',
                    'value' => $this->username
                ]
            );
        }

        if ($this->password) {
            Settings::updateOrCreate(
                ['key' => 'eurolog_password'],
                [
                    'key' => 'eurolog_password',
                    'value' => $this->password
                ]
            );
        }
        $this->dispatch('banner-message', ['style' => 'success', 'message' => 'Opgeslagen.']);
    }

    public function render()
    {
        return view('livewire.euro-log-ftp-login-setting');
    }
}
