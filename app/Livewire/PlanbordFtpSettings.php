<?php

namespace App\Livewire;

use App\Models\Settings;
use Livewire\Component;

class PlanbordFtpSettings extends Component
{
    public $ftpUsername;
    public $ftpPassword;
    public $ftpAddress;

    public function mount()
    {
        $username = Settings::where('key', 'ftp_username')->first();
        $this->ftpUsername = $username ? $username->value : null;

        $password = Settings::where('key', 'ftp_password')->first();
        $this->ftpPassword = $password ? $password->value : null;

        $address = Settings::where('key', 'ftp_address')->first();
        $this->ftpAddress = $address ? $address->value : null;
    }

    public function saveCredentials()
    {
        if ($this->ftpUsername) {
            Settings::updateOrCreate(
                ['key' => 'ftp_username'],
                [
                    'key' => 'ftp_username',
                    'value' => $this->ftpUsername
                ]
            );
        }

        if ($this->ftpPassword) {
            Settings::updateOrCreate(
                ['key' => 'ftp_password'],
                [
                    'key' => 'ftp_password',
                    'value' => $this->ftpPassword
                ]
            );
        }

        if ($this->ftpAddress) {
            Settings::updateOrCreate(
                ['key' => 'ftp_address'],
                [
                    'key' => 'ftp_address',
                    'value' => $this->ftpAddress
                ]
            );
        }
        $this->dispatch('banner-message', ['style' => 'success', 'message' => 'Opgeslagen.']);
    }

    public function render()
    {
        return view('livewire.planbord-ftp-settings');
    }
}
