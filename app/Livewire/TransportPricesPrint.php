<?php

namespace App\Livewire;

use App\Models\TransportPricelist;
use Livewire\Component;

class TransportPricesPrint extends Component
{
    public $priceAgreementId;
    public $priceAgreement;
    public $priceAgreements;

    protected $listeners = ['received' => '$refresh'];

    public function mount()
    {
        $this->priceAgreement = TransportPricelist::where('name', 'Standaard')->first();
        $this->priceAgreements = TransportPricelist::pluck('name', 'id');
    }

    public function load()
    {
        $this->priceAgreement = TransportPricelist::find($this->priceAgreementId);
    }

    public function render()
    {
        return view('livewire.transport-prices-print');
    }
}
