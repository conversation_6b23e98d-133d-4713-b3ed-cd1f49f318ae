<?php

namespace App\Livewire;

use App\Models\Address;
use Livewire\Component;

class CustomerAddresses extends Component
{
    public $customerId;
    public $addresses;
    public $showNewAddressModal = false;
    public $companyName;
    public $name;
    public $streetName;
    public $housenumber;
    public $postalcode;
    public $telephone;
    public $city;
    public $gln;
    public $showEditAddressModal = false;
    public $selectedAddress;
    public $editCompanyName;
    public $editName;
    public $editStreetName;
    public $editHousenumber;
    public $editPostalcode;
    public $editCity;
    public $editGln;

    protected $listeners = ['addressSaved' => 'loadAddresses'];

    public function mount()
    {
        $this->loadAddresses();
    }

    public function loadAddresses()
    {
        $this->addresses = Address::where('customer_id', $this->customerId)->get();
    }

    public function editAddress($addressId)
    {
        $this->selectedAddress = Address::find($addressId);
        $this->editCompanyName = $this->selectedAddress->company_name;
        $this->editName = $this->selectedAddress->name;
        $this->editStreetName = $this->selectedAddress->street;
        $this->editHousenumber = $this->selectedAddress->housenumber;
        $this->editPostalcode = $this->selectedAddress->postal_code;
        $this->editCity = $this->selectedAddress->city;
        $this->editGln = $this->selectedAddress->gln;

        $this->showEditAddressModal = true;
    }

    public function updateAddress()
    {
        $this->selectedAddress->company_name = $this->editCompanyName;
        $this->selectedAddress->name = $this->editName;
        $this->selectedAddress->street = $this->editStreetName;
        $this->selectedAddress->housenumber = $this->editHousenumber;
        $this->selectedAddress->postal_code = str_replace(' ', '', $this->editPostalcode);
        $this->selectedAddress->city = $this->editCity;
        $this->selectedAddress->gln = $this->editGln;
        $this->selectedAddress->save();

        $this->resetFields();
        $this->dispatch('addressSaved');
        $this->showEditAddressModal = false;
    }

    public function addAddress()
    {
        $address = Address::create([
            'type' => 'shipping',
            'name' => $this->name,
            'company_name' => $this->companyName,
            'street' => $this->streetName,
            'housenumber' => $this->housenumber,
            'postal_code' => str_replace(' ', '', $this->postalcode),
            'city' => $this->city,
            'country' => 'NL',
            'gln' => $this->gln,
            'customer_id' => $this->customerId
        ]);

        $this->dispatch('addressSaved');
        $this->showNewAddressModal = false;
        $this->resetFields();
    }

    public function resetFields()
    {
        $this->companyName = '';
        $this->name = '';
        $this->streetName = '';
        $this->housenumber = '';
        $this->postalcode = '';
        $this->city = '';
        $this->gln = '';
        $this->editCompanyName = '';
        $this->editName = '';
        $this->editStreetName = '';
        $this->editHousenumber = '';
        $this->editPostalcode = '';
        $this->editCity = '';
        $this->editGln = '';
    }

    public function render()
    {
        return view('livewire.customer-addresses');
    }
}
