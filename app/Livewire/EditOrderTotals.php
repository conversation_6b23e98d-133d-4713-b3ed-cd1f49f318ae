<?php

namespace App\Livewire;

use App\Models\TransmissionCost;
use Livewire\Component;

class EditOrderTotals extends Component
{
    protected $listeners = [
        'orderChanged' => 'handleOrderChanged',
        'addressChanged' => 'handleAddressChanged',
        'recalculateTransport' => 'recalculateTransport',
        'deliveryDate' => 'handleDeliveryDate',
        'orderItemsChanged' => 'handleOrderItemsChanged',
        'removedOrderlines' => 'handleRemovedOrderlines',
        'orderDate' => 'handleOrderDateChanged'
    ];

    public $order;
    public $subTotalCost;
    public $transportCost;
    public $transportLength;
    public $totalCost;
    public $subTotal;
    public $transport;
    public $total;
    public $changed = false;
    public $address;
    public $deliveryDate;
    public $changedOrderLines = [];
    public $removedOrderlines = [];
    public $orderDate;
    public $revenue;

    public function mount($order)
    {
        $this->order = $order;
        $this->subTotalCost = $order['cost'];
        $this->transportCost = $order['transport_cost'];
        $this->totalCost = $order['total_cost'];
        $this->subTotal = $order['subtotal'];
        $this->transport = $order['transport_price'];
        $this->total = $order['subtotal'] + $order['transport_price'];
        $this->revenue = $order['revenue'];
        $this->address = $order->address;
    }

    /**
     * @param $deliveryDate
     * @return void
     */
    public function handleDeliveryDate($deliveryDate)
    {
        $this->deliveryDate = $deliveryDate;
        $this->changed = true;
    }

    /**
     * @param $order
     * @return void
     */
    public function handleOrderChanged($order)
    {
        $this->changed = true;
    }

    /**
     * @param $address
     * @return void
     */
    public function handleAddressChanged($address)
    {
        $this->changed = true;
        $this->address = $address;
        $this->recalculateTransport();
    }

    /**
     * @param $removedOrderlines
     * @return void
     */
    public function handleRemovedOrderlines($removedOrderlines)
    {
        $this->removedOrderlines = $removedOrderlines;
        $this->recalculateTransport();
        $this->handleOrderItemsChanged();
    }

    /**
     * @return void
     */
    public function handleOrderDateChanged($orderDate)
    {
        $this->orderDate = $orderDate;
        $this->recalculateTransport();
        $this->handleOrderItemsChanged();
    }

    /**
     * @return void
     */
    public function recalculateTransport()
    {
        $this->total = 0.00;

        $freeShipping = false;
        if ($this->transport == 0) {
            $freeShipping = true;
        }

        $this->transport = 0.00;
        $this->transportCost = 0.00;
        $transportLength = 0.00;
        $totalQty = 0;
        foreach ($this->order->orderitems as $orderitem) {
            if (in_array($orderitem->id, $this->removedOrderlines)) {
                continue;
            }
            $transportUnits = $orderitem->qty / $orderitem->product->transport_qty;
            $transportLength += $orderitem->product->transportUnit[$orderitem->product->transmissionData->unit_type] * $transportUnits;
            $totalQty += $orderitem->qty;
        }

        if ($totalQty !== 0) {
            $transmissionCost = TransmissionCost::where('trailer_length', '>=', $transportLength)
                ->orderBy('trailer_length', 'asc')
                ->first();
            $this->transportCost = $transmissionCost->getPriceForPostcode($this->address['postal_code'], $this->address['country']);
        }
        $this->totalCost = $this->subTotalCost + $this->transportCost;

        $transportPriceTier = $this->order->customer->getTransportPriceTier($totalQty);
        $transportPriceTierPrice = $transportPriceTier->getPriceForPostcode($this->address['postal_code'], $this->address['country']);
        $transportPriceTierQty = $transportPriceTier->trailer_spot;
        if ($freeShipping == false) {
            $this->transport = $transportPriceTierPrice * $totalQty;
        }
        $this->total = $this->order->subtotal + $this->transport;
    }

    /**
     * @param $orderline
     * @return void
     */
    public function handleOrderItemsChanged($orderline = null)
    {
        $this->changed = true;
        if ($orderline && !is_array($orderline)) {
            $this->changedOrderLines[$orderline['id']] = $orderline;
        }
        if (is_array($orderline)) {
            foreach ($orderline as $orderLine) {
                $this->changedOrderLines[$orderLine['id']] = $orderLine;
            }
        }
        $this->subTotal = 0.00;
        $this->total = 0.00;
        $this->subTotalCost = 0.00;
        $this->totalCost = 0.00;

        foreach ($this->order->orderitems as $orderitem) {
            if (array_key_exists($orderitem->id, $this->changedOrderLines)) {
                $this->subTotal += $this->changedOrderLines[$orderitem->id]['price'];
                $this->subTotalCost += $this->changedOrderLines[$orderitem->id]['cost'];
            } elseif(in_array($orderitem->id, $this->removedOrderlines)) {
                continue;
            } else {
                $this->subTotal += $orderitem->price_ex_vat;
                $this->subTotalCost += $orderitem->cost;
            }
        }

        $this->total = $this->subTotal + $this->transport;
        $this->totalCost = $this->subTotalCost + $this->transportCost;
    }

    /**
     * @return void
     */
    public function saveOrder()
    {
        if ($this->deliveryDate) {
            $this->order->delivery_date = $this->deliveryDate;
        }

        if ($this->address) {
            $this->order->address_id = $this->address['id'];
        }

        if ($this->orderDate) {
            $this->order->order_date = $this->orderDate;
        }

        foreach ($this->order->orderitems as $orderitem) {
            if (in_array($orderitem->id, $this->removedOrderlines)) {
                $orderitem->delete();
            }
            if (array_key_exists($orderitem->id, $this->changedOrderLines)) {
                $orderitem->cost = $this->changedOrderLines[$orderitem->id]['cost'];
                $orderitem->price_ex_vat = $this->changedOrderLines[$orderitem->id]['price'];
                $orderitem->qty = $this->changedOrderLines[$orderitem->id]['qty'];
                $orderitem->save();
            }
        }
        $this->order->cost = $this->subTotalCost;
        $this->order->total_ex_vat = $this->total;
        $this->order->transport_cost = $this->transportCost;
        $this->order->transport_price = $this->transport;
        $this->order->total_cost = $this->totalCost;
        $this->order->subtotal = $this->subTotal;

        $this->order->save();
        $this->order->load('orderitems');
        $this->dispatch('orderSaved');
        $this->changed = false;

        $this->dispatch('banner-message', ['style' => 'success', 'message' => 'Bestelling opgeslagen.']);
    }

    public function render()
    {
        return view('livewire.edit-order-totals');
    }
}
