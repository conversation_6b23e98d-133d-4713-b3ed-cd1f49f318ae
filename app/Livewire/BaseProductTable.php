<?php

namespace App\Livewire;

use App\Models\BaseProduct;
use Livewire\Component;
use Yajra\DataTables\Facades\DataTables;

class BaseProductTable extends Component
{
    public $showModal = false;
    public $name;
    public $price;

    public function createBaseProduct()
    {
        BaseProduct::create([
            'name' => $this->name,
            'price' => $this->price,
        ]);
        return redirect()->route('product.base');
    }

    public function closeModal()
    {
        return redirect()->route('product.base');
    }

    public function getProducts()
    {
        $query = BaseProduct::query();

        return DataTables::of($query)->make(true);
    }

    public function render()
    {
        return view('livewire.base-product-table');
    }
}
