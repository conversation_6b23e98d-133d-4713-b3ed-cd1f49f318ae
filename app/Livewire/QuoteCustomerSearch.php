<?php

namespace App\Livewire;

use App\Models\Customer;
use Illuminate\Database\Eloquent\Collection;
use Livewire\Component;

class QuoteCustomerSearch extends Component
{
    public $search = '';
    public $customers;
    public $selectedCustomer;
    public $showModal = false;
    public $customerName;
    public $customerEmail;

    public function mount()
    {
        $this->customers = new Collection();
    }

    public function updatedSearch()
    {
        $this->customers = Customer::where('name', 'like', '%'.$this->search.'%')->get();
    }

    public function selectCustomer(Customer $customer)
    {
        $this->selectedCustomer = $customer;

        $this->search = '';
        $this->customers = new Collection();
        $this->showModal = true;
    }

    public function saveCustomer()
    {
        $customerData = [
            'id' => $this->selectedCustomer->id,
            'name' => $this->customerName,
            'email' => $this->customerEmail
        ];
        $this->dispatch('quoteCustomerSelected', $customerData);
        $this->showModal = false;
    }

    public function render()
    {
        return view('livewire.quote-customer-search');
    }
}
