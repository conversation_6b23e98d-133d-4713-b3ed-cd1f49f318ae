<?php

namespace App\Livewire;

use App\Models\Order;
use Livewire\Component;
use Yajra\DataTables\Facades\DataTables;

class CustomerOrders extends Component
{
    public $customerId;

    public function mount($customerId)
    {
        $this->customerId = $customerId;
    }

    public function getOrders($customerId)
    {
        $query = Order::where('customer_id', $customerId)->with('customer', 'shipment', 'address')->get();

        return DataTables::of($query)
            ->addColumn('hasShipment', function ($order) {
                return $order->hasShipment;
            })
            ->make(true);
    }

    public function render()
    {
        return view('livewire.customer-orders');
    }
}
