<?php

namespace App\Livewire;

use App\Models\Customer;
use App\Models\Product;
use App\Models\PurchaseOrder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class CreatePurchaseOrder extends Component
{
    public $customerSearch = '';
    public $customers;
    public $selectedCustomer;
    public $deliveryDate;
    public $loadDate;
    public $selectedProduct;
    public $searchProduct = '';
    public $products;
    public $qty;
    public $purchasePrice = 0.00;
    public $orderLines = [];
    public $showModal = false;
    private $orderLineCount = 1;
    public $stockPlaces;
    public $selectedStockPlace;

    public function mount()
    {
        $this->customers = new Collection();
        $this->products = new Collection();
    }

    public function updatedCustomerSearch()
    {
        $this->customers = Customer::where('name', 'like', '%'.$this->customerSearch.'%')->get();
    }

    public function updatedSearchProduct()
    {
        $this->products = Product::where('name', 'like', '%'.$this->searchProduct.'%')->get();
    }

    public function selectProduct(Product $product)
    {
        $this->selectedProduct = $product;
        if ($product->storageProduct) {
            $this->storageProduct = $product->storageProduct->toArray();
        }
        $this->stockPlaces = $product->stockPlaces->toArray();
        $test = 'test';
    }

    public function addOrderLine()
    {
        $this->orderLines[] = [
            'id' => $this->orderLineCount,
            'product' => $this->selectedProduct,
            'unit_price' => $this->purchasePrice,
            'qty' => $this->qty,
            'stock_place' => $this->selectedStockPlace
        ];
        $this->resetFields();
        $this->showModal = false;
    }

    public function saveOrder()
    {
        try {
            $purchaseOrder = PurchaseOrder::create([
                'user_id' => Auth::id(),
                'customer_id' => $this->selectedCustomer->id,
                'status' => 'ORDERED',
                'delivery_date' => $this->deliveryDate,
                'transport_cost' => 0.00
            ]);

            foreach($this->orderLines as $orderLine) {
                $purchaseOrder->purchaseOrderlines()->create([
                    'product_id' => $orderLine['product']['id'],
                    'qty' => $orderLine['qty'],
                    'purchase_price' => $orderLine['unit_price'],
                    'stock_place_id' => ($orderLine['stock_place'] == 'new') ? null : $orderLine['stock_place']
                ]);
            }
        } catch (\Exception $e) {

        }

        return redirect()->route('purchase-order.index');
    }

    public function resetFields()
    {
        $this->selectedProduct = null;
        $this->searchProduct = '';
        $this->selectedStockPlace = null;
        $this->stockPlaces = [];
        $this->products = new Collection();
    }

    public function selectCustomer(Customer $customer)
    {
        $this->selectedCustomer = $customer;
        $this->customerSearch = '';
        $this->customers = new Collection();
    }

    public function render()
    {
        return view('livewire.create-purchase-order');
    }
}
