<?php

namespace App\Livewire;

use Livewire\Component;

class OrderDeliveryDate extends Component
{
    public $deliveryDate;
    public $reference;

    public function updatedDeliveryDate()
    {
        $this->dispatch('deliveryDate', $this->deliveryDate);
    }

    public function updatedReference()
    {
        $this->dispatch('reference', $this->reference);
    }

    public function render()
    {
        return view('livewire.order-delivery-date');
    }
}
