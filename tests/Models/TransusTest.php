<?php
declare(strict_types=1);

namespace Tests\Models;

use App\Models\Transus;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\Customer;
use App\Models\Product;
use App\Models\Order;


class TransusTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_successfully_parses_and_creates_an_order()
    {

        config(['some_config_key' => 'value']);
        // Arrange
        $xmlString = '<Message>
                        <OrderNumberBuyer>12345</OrderNumberBuyer>
                        <InvoiceeGLN>4000000000002</InvoiceeGLN>
                        <RequestedDeliveryDate>20240125</RequestedDeliveryDate>
                        <OrderDate>20240101</OrderDate>
                        <Article>
                            <GTIN>1234567890</GTIN>
                            <OrderedQuantity>10</OrderedQuantity>
                        </Article>
                      </Message>';

        // Mocking Customer
        $customer = Customer::factory()->create([
            'gln' => '4000000000002'
        ]);

        // Mocking Product
        $product = Product::factory()->create([
            'gtin' => '1234567890'
        ]);

        // Mocking Transus model with XML message
        $transus = Transus::factory()->create([
            'message' => $xmlString
        ]);

        // Act
        $transus->reParseXml();

        // Assert
        $order = Order::where('customer_id', $customer->id)->first();
        $this->assertNotNull($order); // Order should be created
        $this->assertEquals($order->customer_id, $customer->id);
        $this->assertEquals($order->delivery_date, '2024-01-25');
        $this->assertCount(1, $order->orderitems); // One article in the order
        $this->assertEquals($order->orderitems->first()->product_id, $product->id);
        $this->assertEquals($order->orderitems->first()->price_ex_vat, 1000); // 10 * 100
    }
}
