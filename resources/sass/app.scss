@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

[x-cloak] {
  display: none;
}
// Fonts
@import url('https://fonts.bunny.net/css?family=Nunito');

// Variables

// Bootstrap
@import '../../node_modules/bootstrap/scss/bootstrap';

// DataTables
@import '../../node_modules/bootstrap-icons/font/bootstrap-icons.css';
@import '../../node_modules/datatables.net-bs5/css/dataTables.bootstrap5.min.css';
@import '../../node_modules/datatables.net-buttons-bs5/css/buttons.bootstrap5.min.css';
@import '../../node_modules/datatables.net-select-bs5/css/select.bootstrap5.css';

.bg-green-500 {
  background-color: #84cc16;
}

.bg-green-600 {
  background-color: #16a34a;
}

.bg-indigo-200 {
  background-color: #c7d2fe;
}

.w-1-4 {
  width: 25%;
}

.w-1-3 {
  width: 33.3%;
}

.w-2-3 {
  width: 66.6%;
}

.toggle {
  cursor: pointer;
  display: inline-block;
}

.toggle-switch {
  display: inline-block;
  background: #ccc;
  border-radius: 16px;
  width: 58px;
  height: 32px;
  position: relative;
  vertical-align: middle;
  transition: background 0.25s;
}
.toggle-switch:before, .toggle-switch:after {
  content: "";
}
.toggle-switch:before {
  display: block;
  background: linear-gradient(to bottom, #fff 0%, #eee 100%);
  border-radius: 50%;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.25);
  width: 24px;
  height: 24px;
  position: absolute;
  top: 4px;
  left: 4px;
  transition: left 0.25s;
}
.toggle:hover .toggle-switch:before {
  background: linear-gradient(to bottom, #fff 0%, #fff 100%);
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.5);
}
.toggle-checkbox:checked + .toggle-switch {
  background: #56c080;
}
.toggle-checkbox:checked + .toggle-switch:before {
  left: 30px;
}

.toggle-checkbox {
  position: absolute;
  visibility: hidden;
}

.toggle-label {
  margin-left: 5px;
  position: relative;
  top: 2px;
}

.button-tab::before {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: 10px solid #94a3b8;
  display: none;
}
.button-tab-active::before {
  display: block;
}

.pricelist-tab {
  position: absolute;
  top: -64px;
}

.tabActive {
  color: #000;
}

.pricelist-button-tab {
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}

#quote-lines ul {
  list-style: disc;
}