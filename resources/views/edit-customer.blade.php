<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ $customer->name }}
            </h2>
{{--            <div class="flex align-content-center">--}}
{{--                <a href="{{ route('communication.view', ['customer' => $customer->id]) }}" class="inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150" style="text-decoration: none">--}}
{{--                    <i class="fa-solid fa-comments mr-2" style="color: #ffffff;"></i> Communicatie--}}
{{--                </a>--}}
{{--                <a href="{{ route('customer.prices', ['customer' => $customer->id]) }}" class="inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150" style="text-decoration: none">--}}
{{--                    <i class="fa-solid fa-table-list mr-2" style="color: #ffffff;"></i> Prijslijst--}}
{{--                </a>--}}
{{--            </div>--}}
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                <form method="POST" class="w-full max-w-xl" action="{{ route('customer.update', ['id' => $customer->id]) }}">
                    @csrf
                    <div class="flex items-center mb-6">
                        <div class="w-1/3">
                            <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4" for="exact_id" value="{{ __('Exact ID') }}" />
                        </div>
                        <div class="w-2/3">
                            <x-input disabled="true" id="exact_id" class="block mt-1 w-full" type="text" name="exact_id" value="{{ $customer->exact_id }}"  />
                        </div>
                    </div>
                    <div class="flex items-center mb-6">
                        <div class="w-1/3">
                            <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4" for="gln" value="{{ __('GLN') }}" />
                        </div>
                        <div class="w-2/3">
                            <x-input id="gln" class="block mt-1 w-full" type="text" name="gln" value="{{ is_array($customer->gln) ? implode(',', $customer->gln) : $customer->gln }}"  />
                        </div>
                    </div>
                    <div class="flex items-center mb-6">
                        <div class="w-1/3">
                            <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4" for="name" value="{{ __('Naam') }}" />
                        </div>
                        <div class="w-2/3">
                            <x-input disabled="true" id="name" class="block mt-1 w-full" type="text" name="name" value="{{ $customer->name }}"  />
                        </div>
                    </div>
                    <div class="flex items-center mb-6">
                        <div class="w-1/3">
                            <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4" for="address" value="{{ __('Adres') }}" />
                        </div>
                        <div class="w-2/3">
                            <x-input disabled="true" id="address" class="block mt-1 w-full" type="text" name="address" value="{{ $customer->address }}"  />
                        </div>
                    </div>
                    <div class="flex items-center mb-6">
                        <div class="w-1/3">
                            <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4" for="postal_code" value="{{ __('Postcode') }}" />
                        </div>
                        <div class="w-2/3">
                            <x-input disabled="true" id="postal_code" class="block mt-1 w-full" type="text" name="postal_code" value="{{ $customer->postal_code }}"  />
                        </div>
                    </div>
                    <div class="flex items-center mb-6">
                        <div class="w-1/3">
                            <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4" for="city" value="{{ __('Stad') }}" />
                        </div>
                        <div class="w-2/3">
                            <x-input disabled="true" id="city" class="block mt-1 w-full" type="text" name="city" value="{{ $customer->city }}"  />
                        </div>
                    </div>
                    <div class="flex items-center mb-6">
                        <div class="w-1/3">
                            <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4" for="country" value="{{ __('Land') }}" />
                        </div>
                        <div class="w-2/3">
                            <x-input disabled="true" id="country" class="block mt-1 w-full" type="text" name="country" value="{{ $customer->country }}"  />
                        </div>
                    </div>
                    <div class="flex items-center mb-6">
                        <div class="w-1/3">
                            <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4" for="email" value="{{ __('Email') }}" />
                        </div>
                        <div class="w-2/3">
                            <x-input disabled="true" id="email" class="block mt-1 w-full" type="text" name="email" value="{{ $customer->email }}"  />
                        </div>
                    </div>
                    <div class="flex items-center mb-6">
                        <div class="w-1/3">

                        </div>
                        <div><x-button>Opslaan</x-button></div>
                    </div>
                </form>
                <h5>Prijslijst(en)</h5>
                <div>
                    <p>Klant is gekoppeld aan de volgende prijslijst(en)</p>
                    <table class="table" style="width: 50%;">
                        <thead>
                            <tr>
                                <th>Prijslijst naam</th>
                                <th>Geldig van</th>
                                <th>Geldig tot</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($customer->pricelists as $pricelist)
                                <tr>
                                    <td><a href="{{ route('pricelist.edit', ['pricelist' => $pricelist->id]) }}">{{ $pricelist->name }}</a></td>
                                    <td>{{ $pricelist->from }}</td>
                                    <td>{{ $pricelist->to }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                    <hr>

                <div x-data="{ tab: '{{ request()->input('taskcompleted') == '1' ? 'second' : 'first' }}' }">
                    <nav>
                        <div class="flex-row">
                            <button :class="{ 'tabActive bg-gray-400 button-tab-active': tab === 'first', 'bg-gray-200': tab !== 'first' }" class="rounded px-4 py-2 mr-2 relative  button-tab" @click="tab = 'first'">Bestellingen</button>
                            <button :class="{ 'tabActive bg-gray-400 button-tab-active': tab === 'second', 'bg-gray-200': tab !== 'second' }" class="rounded px-4 py-2 mr-2 relative  button-tab" @click="tab = 'second'">Adressen</button>
                            <button :class="{ 'tabActive bg-gray-400 button-tab-active': tab === 'third', 'bg-gray-200': tab !== 'third' }" class="rounded px-4 py-2 mr-2 relative  button-tab" @click="tab = 'third'">Transmission</button>
                        </div>
                    </nav>
                    <x-section-border />
                    <div>
                        <div x-show="tab === 'first'">
                            <livewire:customer-orders :customerId="$customer->id" />
                        </div>

                        <div x-show="tab === 'second'">
                            <livewire:customer-addresses :customerId="$customer->id" />
                        </div>

                        <div x-show="tab === 'third'">
                            <div class="flex items-center mb-6">
                                <div class="w-2/3 flex">
                                    <x-info-balloon>
                                        <h4>Transportprijs afspraken</h4>
                                        <p>Wanneer achter de titel [BASE] staat betekent dat deze klant geen afwijkende afspraken heeft.<br>
                                            De kolommen Prijs NL t/m Prijs WE zijn de prijzen waar mee gerekend wordt voor onze eigen administratie.
                                        </p>
                                        Afkortingen:
                                        <ul>
                                            <li>NL: Nederland</li>
                                            <li>BE: Belgie</li>
                                            <li>ZV: Zuid Vlaanderen</li>
                                            <li>WE: Wadden Eilanden</li>
                                        </ul>
                                    </x-info-balloon>
                                    <h5 class="mr-3 ml-3">Transport Prijs Afspraak {{ $customer->transportPriceAgreement->is_base === 1 ? '[BASE]' : '' }}</h5>

                                    @if($customer->transportPriceAgreement->is_base === 1)
                                        <a href="{{ route('transportagreement.new', ['customer' => $customer->id]) }}" class="inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150" style="text-decoration: none"><i class="fa-solid fa-plus mr-2" style="color: #ffffff;"></i>Nieuw</a>
                                    @else
                                        <a style="margin-right: 15px; text-decoration: none" href="{{ route('transportagreement.edit', ['transportPriceAgreement' => $customer->transportPriceAgreement->id]) }}" class="inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150"><i class="fa-solid fa-pen-to-square mr-2" style="color: #ffffff;"></i>Aanpassen</a>
                                        <livewire:delete-transport-agreement :transportPriceAgreementId="$customer->transportPriceAgreement->id" :customerId="$customer->id" />
                                    @endif
                                </div>
                            </div>
{{--                            <h6 class="font-bold">Diesel toeslag: {{ $customer->transportPriceAgreement->fuelPremium->percentage }}%</h6>--}}
                            <table class="table-fixed">
                                <thead class="border-b font-bold">
                                <tr>
                                    <th scope="col" class="py-6" width="130">Aantal pallets</th>
                                    <th scope="col" class="py-6" width="130">Prijs NL</th>
                                    <th scope="col" class="py-6" width="130">Prijs BE</th>
                                    <th scope="col" class="py-6" width="130">Prijs ZV</th>
                                    <th scope="col" class="py-6" width="130">Prijs WE</th>
                                </tr>
                                </thead>
                                <tbody>
                                @foreach($customer->transportPriceAgreement->priceTiers as $index => $priceTier)
                                    <tr class="border-b @if($index % 2 === 0) bg-gray-100 @endif">
                                        <td>{{ $priceTier->trailer_spot }}</td>
                                        <td>&euro;{{ $priceTier->price_nl }}</td>
                                        <td>&euro;{{ $priceTier->price_be }}</td>
                                        <td>&euro;{{ $priceTier->price_zv }}</td>
                                        <td>&euro;{{ $priceTier->price_we }}</td>
                                    </tr>
                                @endforeach
                                </tbody>
                            </table>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
