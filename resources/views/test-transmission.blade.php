<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Transmission - Order {{ $order->id }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .header {
            border-bottom: 2px solid #e5e5e5;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #333;
            margin: 0;
        }
        .order-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .section {
            margin-bottom: 30px;
        }
        .section h2 {
            color: #444;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }
        .json-container {
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            overflow-x: auto;
        }
        pre {
            margin: 0;
            white-space: pre-wrap;
            word-wrap: break-word;
            font-size: 12px;
            line-height: 1.4;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #f5c6cb;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #c3e6cb;
            margin-bottom: 20px;
        }
        .close-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #dc3545;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .close-btn:hover {
            background-color: #c82333;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <button class="close-btn" onclick="window.close()">Close Window</button>
    
    <div class="container">
        <div class="header">
            <h1>Test Transmission Results</h1>
            <div class="order-info">
                <strong>Order ID:</strong> {{ $order->id }} | 
                <strong>Customer:</strong> {{ $order->customer->name }} | 
                <strong>Delivery Date:</strong> {{ $order->delivery_date }}
            </div>
        </div>

        @if(isset($error))
            <div class="error">
                <strong>Error:</strong> {{ $error }}
            </div>
        @endif

        @if(isset($success) && $success)
            <div class="success">
                <strong>Test Completed Successfully!</strong> The API call was made successfully. Below you can see the request data sent and the response received.
            </div>

            <div class="grid">
                <div class="section">
                    <h2>Request Data (Sent to API)</h2>
                    <div class="json-container">
                        <pre>{{ json_encode($requestData, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES) }}</pre>
                    </div>
                </div>

                <div class="section">
                    <h2>Response Data (Received from API)</h2>
                    <div class="json-container">
                        <pre>{{ json_encode($responseData, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES) }}</pre>
                    </div>
                </div>
            </div>

            @if(isset($responseData['status']) && $responseData['status'] === 200)
                <div class="section">
                    <h2>Response Summary</h2>
                    <div style="background-color: #d4edda; padding: 15px; border-radius: 5px;">
                        <p><strong>Status:</strong> Success ({{ $responseData['status'] }})</p>
                        @if(isset($responseData['data']['transport_number']))
                            <p><strong>Transport Number:</strong> {{ $responseData['data']['transport_number'] }}</p>
                        @endif
                        @if(isset($responseData['data']['tracking_url']))
                            <p><strong>Tracking URL:</strong> <a href="{{ $responseData['data']['tracking_url'] }}" target="_blank">{{ $responseData['data']['tracking_url'] }}</a></p>
                        @endif
                    </div>
                </div>
            @elseif(isset($responseData['status']))
                <div class="section">
                    <h2>Response Summary</h2>
                    <div style="background-color: #f8d7da; padding: 15px; border-radius: 5px;">
                        <p><strong>Status:</strong> Error ({{ $responseData['status'] }})</p>
                        @if(isset($responseData['meta']['error_list']))
                            <p><strong>Errors:</strong></p>
                            <ul>
                                @foreach($responseData['meta']['error_list'] as $error)
                                    <li>{{ $error[1] ?? $error }}</li>
                                @endforeach
                            </ul>
                        @endif
                    </div>
                </div>
            @endif
        @endif

        <div class="section" style="text-align: center; margin-top: 40px;">
            <p><em>This was a test transmission. No actual shipment was created and no data was saved to the database.</em></p>
        </div>
    </div>
</body>
</html>
