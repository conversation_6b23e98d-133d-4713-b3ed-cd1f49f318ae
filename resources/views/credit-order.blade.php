<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between">
            <div class="flex">
                <h2 class="font-semibold text-xl text-gray-800 leading-tight mr-3">
                    Credit voor bestelling: {{ $order->id }}
                </h2>
                <x-info-balloon>
                    <h4>Credit</h4>
                    <p>Een credit order wordt pas aangemaakt op het moment dat deze wordt opgeslagen.</p>
                </x-info-balloon>
            </div>
            <div class="mr-3">
                Status: <strong>{{ $order->status }}</strong>
            </div>

        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                <div class="flex" style="height: 200px;">
                    <div id="billing" style="margin-right: 100px;">
                        <strong>Factuuradres</strong><br>
                        {{ $order->customer->exact_id }}<br>
                        {{ $order->customer->name }}<br>
                        {{ $order->customer->address }}<br>
                        {{ $order->customer->postal_code }} {{ $order->customer->city }}<br>
                        {{ $order->customer->email }}
                    </div>
                    <div id="shipping">
                        <strong>Verzendadres</strong><br>
                        {{ $order->address->company_name }}<br>
                        {{ $order->address->name }}<br>
                        {{ $order->address->street  }} {{ $order->address->housenumber }}<br>
                        {{ $order->address->postal_code }} {{ $order->address->city }}<br>
                        {{ $order->address->email }}<br>
                    </div>
                </div>
                <div>
                    <livewire:credit-order :orderid="$order->id" />
                </div>
            </div>
        </div>

    </div>
</x-app-layout>
