<html>
<head>
  <title>Prijslijst</title>
  <style>
    @media print {
      body {
        width: 21cm;
        height: 29.7cm;
        /*margin: 30mm 45mm 30mm 45mm;*/
        -webkit-print-color-adjust:exact !important;
        print-color-adjust:exact !important;
      }
      .toppack {
        display: flex;
        justify-content: space-between;
      }

      textarea {
        border: none;
        font-family: 'Helvetica', 'Arial', sans-serif;
        margin-top: 20px;
      }

      input {
        background: #ccc;
        border: none;
        font-family: 'Helvetica', 'Arial', sans-serif;
        font-size: 16px;
        width: 100%;
        font-weight: bold;
        margin-left: -1px;
      }

      .move-buttons {
        display: none;
      }

      .select-pricelist {
        display: none;
      }
    }
    .toppack {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
    }
    body {
      font-family: 'Helvetica', 'Arial', sans-serif;
      /*font-weight: bold;*/
    }

    input {
      width: 100%;
    }



    .top-left-pack {
      display: flex;
      flex-direction: column;
      /*font-weight: bold;*/
      justify-content: space-between;
    }

    .top-right-pack {
      /*font-weight: bold;*/
      color: #cccccc;
    }

    .toppadding {
      padding-top: 15px;
    }
    .addresses {
      display: flex;
      margin-top: 30px;
      justify-content: space-between;
    }
      .addresses span {
        padding-bottom: 5px;
        display: block;
      }
    .shipping {
      width: 320px;
    }
    .billing {
      width: 300px;
    }
    table {
      /*font-weight: bold;*/
    }

    .orderlines {
      margin-top: 40px;
    }

    .orderlines table th {
      border-bottom: 1px solid #000;
    }

    .product-tiers {
      margin-bottom: 20px;
      border: 1px solid #ccc;
    }

    .product-top {
      padding-top: 5px;
      padding-bottom: 5px;
      background: #ccc;
    }


    .tiertable tr:nth-child(even) {
      background: #ccc;
    }

    .transporttable tr:nth-child(even) {
      background: #ccc;
    }

    .product {
      break-inside: avoid;
    }

    .pelfin-name {
      text-align: center;
      width: 100%;
    }

  </style>
  @livewireStyles

  @stack('scripts')
</head>
<body>
  <div class="container">
    <div class="pelfin-name">
      <small>Pelfin, onderdeel van Logi.Span Retail B.V., Winsum (9951 SJ), Het Aanleg 21. KvK: 02090558 | BTW: NL815064408B01</small><br><br>
    </div>
    <div class="toppack">
      <div class="top-left-pack">
        <div>
          <img alt="logo" src="https://www.bustotaal.nl/wp-content/uploads/pelfin-logo.png" width="300"/>
        </div>
      </div>
      <div>
          Deze prijslijst is geldig van {{ Carbon\Carbon::parse($pricelist->from)->format('d-m-Y') }} tot {{ Carbon\Carbon::parse($pricelist->to)->format('d-m-Y') }} <br>
        Alle prijzen zijn exclusief BTW
      </div>
    </div>
    <div class="w-full" id="product-container">
        @foreach($products as $product)


              @if(array_key_exists($product->id, $dropShipProductPrices))
          <div class="rounded bg-gray-200 p-2 mb-4 product">
            <div class="move-buttons">
              <button onclick="moveUp(this)">Up</button>
              <button onclick="moveDown(this)">Down</button>
            </div>
            <div class="flex justify-between">
              <div class="product-top">
                <table>
                  <tr>
                    <td style="width: 160px">SKU:</td>
                    <td>{{ $product->sku }}</td>
                  </tr>
                  <tr>
                    <td>Naam:</td>
                    <td width="600">
                      <strong><input value="{{ $product->name }} (dropship)" /></strong>
                    </td>
                  </tr>
                  <tr>
                    <td>Transport:</td>
                    <td> {{ $dropShipCheckboxValues[$product->id] ? 'Gratis verzending' : 'Zie transportprijzen tabel onderaan dit document'}}</td>
                  </tr>
                  @if($product->transport_qty > 1)
                    <tr>
                      <td>Eenheden per pallet:</td>
                      <td>
                        {{ $product->transport_qty }}
                      </td>
                    </tr>
                  @endif
                </table>
              </div>

              <div class=" pl-3 product-tiers">
                <table class="table-fixed mb-4 tiertable" style="width: 100%" cellspacing="0" cellpadding="0">
                  <thead class="border-b font-bold">
                  <tr style="text-align: left">
                    <th scope="col" class="py-1" style="width: 33%; padding-left: 5px;">Van</th>
                    <th scope="col" class="py-1" style="width: 33%">Tot</th>
                    <th scope="col" class="py-1" style="width: 33%">Prijs per eenheid</th>
                  </tr>
                  </thead>
                  <tbody>

                    @foreach($dropShipProductPrices[$product->id] as $priceTier)
                      <tr style="border-top: 1px solid #000" >
                        <td style="padding-left: 5px;">{{ $priceTier['from_qty'] }}</td>
                        <td>{{ $priceTier['to_qty'] }}</td>
                        <td>&euro; {{ number_format($priceTier['price'], 2) }}</td>
                      </tr>

                    @endforeach
                  </tbody>
                </table>

              </div>
            </div>
          </div>
              @endif

              @if(array_key_exists($product->id, $productPrices))
                <div class="rounded bg-gray-200 p-2 mb-4 product">
                  <div class="move-buttons">
                    <button onclick="moveUp(this)">Up</button>
                    <button onclick="moveDown(this)">Down</button>
                  </div>
                  <div class="flex justify-between">
                  <div class="product-top">
                    <table>
                      <tr>
                        <td style="width: 80px">SKU:</td>
                        <td>{{ $product->sku }}</td>
                      </tr>
                      <tr>
                        <td>Naam:</td>
                        <td width="600"><strong><input value="{{ $product->name }} (winkelbevoorrading)" /></strong></td>
                      </tr>
                      <tr>
                        <td>Transport:</td>
                        <td> {{ $checkboxValues[$product->id] ? 'Gratis verzending' : 'Zie transportprijzen tabel onderaan dit document'}}</td>
                      </tr>
                      @if($product->transport_qty > 1)
                        <tr>
                          <td>Eenheden per pallet:</td>
                          <td>
                            {{ $product->transport_qty }}
                          </td>
                        </tr>
                      @endif
                    </table>
                  </div>

                  <div class=" pl-3 product-tiers">
                    <table class="table-fixed mb-4 tiertable" style="width: 100%" cellspacing="0" cellpadding="0">
                      <thead class="border-b font-bold">
                      <tr style="text-align: left">
                        <th scope="col" class="py-1" style="width: 33%; padding-left: 5px;">Van</th>
                        <th scope="col" class="py-1" style="width: 33%">Tot</th>
                        <th scope="col" class="py-1" style="width: 33%">Prijs per eenheid</th>
                      </tr>
                      </thead>
                      <tbody>

                      @foreach($productPrices[$product->id] as $priceTier)
                        <tr style="border-top: 1px solid #000" >
                          <td style="padding-left: 5px;">{{ $priceTier['from_qty'] }}</td>
                          <td>{{ $priceTier['to_qty'] }}</td>
                          <td>&euro; {{ number_format($priceTier['price'], 2) }}</td>
                        </tr>

                      @endforeach
                      </tbody>
                    </table>

                  </div>
                  </div></div>
              @endif



        @endforeach
      <livewire:transport-prices-print />

    </div>
    <div class="free-text">
      <textarea name="text" id="" cols="110" rows="10"></textarea>
    </div>
  </div>
  <script>
    function moveUp(button) {
      const productDiv = button.closest('.product');
      const previousDiv = productDiv.previousElementSibling;
      if (previousDiv) {
        productDiv.parentNode.insertBefore(productDiv, previousDiv);
      }
    }

    function moveDown(button) {
      const productDiv = button.closest('.product');
      const nextDiv = productDiv.nextElementSibling;
      if (nextDiv) {
        productDiv.parentNode.insertBefore(nextDiv, productDiv);
      }
    }
  </script>
  @livewireScripts
</body>
</html>