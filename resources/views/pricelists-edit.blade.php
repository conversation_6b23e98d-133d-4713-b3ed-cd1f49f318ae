<x-app-layout>
    <x-slot name="header">
        <div class="flex align-items-baseline">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight mr-3">
                Prijslijst bewerken: {{ $pricelist->name }}
            </h2>
        </div>
    </x-slot>

    <div class="py-12" style="padding-top: 6rem;">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white shadow-xl sm:rounded-lg p-6">
                <div x-data="{ tab: 'first' }" class="relative">
                    <nav class="pricelist-tab">
                        <div class="flex-row">
                            <button :class="{ 'tabActive bg-white button-tab-active': tab === 'first', 'bg-gray-200': tab !== 'first' }" class="px-4 py-2 mr-2 relative  pricelist-button-tab" @click="tab = 'first'">Prijslijst</button>
                            <button :class="{ 'tabActive bg-white button-tab-active': tab === 'second', 'bg-gray-200': tab !== 'second' }" class="px-4 py-2 mr-2 relative  pricelist-button-tab" @click="tab = 'second'">Klanten</button>
                        </div>
                    </nav>
                    <div>
                        <div x-show="tab === 'first'">
                            <livewire:edit-pricelist :pricelistId="$pricelist->id" />
                        </div>
                        <div x-show="tab === 'second'">
                            <livewire:pricelist-customer :pricelistid="$pricelist->id" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
