<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Transport Prijsafspraak Aanpassen
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">

                <form method="POST" class="w-full" action="{{ route('transportagreement.update', ['transportPriceAgreement' => $transportPriceAgreement->id]) }}">
                    @csrf
                    <?php $count = 1; ?>
{{--                    <div class="flex items-center mb-6">--}}
{{--                        <div class="w-1/3">--}}
{{--                            <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4" for="fuel_premium" value="{{ __('Diesel toeslag') }}" />--}}
{{--                        </div>--}}
{{--                        <div class="w-1/3">--}}
{{--                            <x-input id="fuel_premium" class="block mt-1 w-full" type="text" name="fuel_premium" value="{{ $transportPriceAgreement->fuelPremium->percentage }}"  />--}}
{{--                        </div>--}}
{{--                    </div>--}}

                    <div class="flex justify-between">
                        <span class="block w-full ml-6">Aantal pallets</span>
                        <span class="block w-full">Prijs NL</span>
                        <span class="block w-full">Prijs BE</span>
                        <span class="block w-full">Prijs ZV</span>
                        <span class="block w-full">Prijs WE</span>
                    </div>
                    <div id="tier-prices">
                    @foreach($transportPriceAgreement->priceTiers as $tier)
                        <div class="flex">
                            <x-input id="id" class="block mt-1 w-full mx-2" type="hidden" name="tier[{{$count}}][id]" value="{{ $tier->id }}"  />
                            <x-input id="name" class="block mt-1 w-full mx-2" type="text" name="tier[{{$count}}][spot]" value="{{ $tier->trailer_spot }}"  />
                            <x-input id="name" class="block mt-1 w-full mx-2" type="text" name="tier[{{$count}}][price_nl]" value="{{ $tier->price_nl }}"  />
                            <x-input id="name" class="block mt-1 w-full mx-2" type="text" name="tier[{{$count}}][price_be]" value="{{ $tier->price_be }}"  />
                            <x-input id="name" class="block mt-1 w-full mx-2" type="text" name="tier[{{$count}}][price_zv]" value="{{ $tier->price_zv }}"  />
                            <x-input id="name" class="block mt-1 w-full mx-2" type="text" name="tier[{{$count}}][price_we]" value="{{ $tier->price_we }}"  />
                        </div>
                        <?php $count++; ?>
                    @endforeach
                    </div>
                <div class="p-2">
                    <button onclick="event.preventDefault(); addTier()" id="add-tier" class="rounded-full text-white" style="width: 35px; height: 35px; background-color: green; font-size: 26px; line-height: 35px;">+</button>
                </div>
                    <div class="flex items-center mt-6">
                            <x-button>
                                {{ __('Opslaan') }}
                            </x-button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <script>
        var tierCount = '<?php echo $count; ?>';
        function addTier() {
            var newTier = '<div class="flex">' +
                    '<input class="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm block mt-1 w-full mx-2" type="text" name="newtier['+ tierCount +'][spot]">' +
                    '<input class="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm block mt-1 w-full mx-2" type="text" name="newtier['+ tierCount +'][price_nl]">' +
                    '<input class="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm block mt-1 w-full mx-2" type="text" name="newtier['+ tierCount +'][price_be]">' +
                    '<input class="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm block mt-1 w-full mx-2" type="text" name="newtier['+ tierCount +'][price_zv]">' +
                    '<input class="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm block mt-1 w-full mx-2" type="text" name="newtier['+ tierCount +'][price_we]">' +
                    '</div>';
            $('#tier-prices').append(newTier);
            tierCount++;
        }
    </script>
</x-app-layout>
