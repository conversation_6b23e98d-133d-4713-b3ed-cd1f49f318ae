<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Prijslijst
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                @foreach ($products as $product)
                    <h5>{{ $product->name }}</h5>
                    @if($product->customerPrices->isEmpty())
                        <p>&euro;{{ $product->price }}</p>
                    @else
                        @foreach($product->customerPrices as $price)
                            <p>{{ $price->from_qty }} - {{ $price->to_qty }} &euro;{{ $price->price }}</p>
                        @endforeach
                    @endif
                @endforeach
            </div>
        </div>
    </div>
</x-app-layout>
