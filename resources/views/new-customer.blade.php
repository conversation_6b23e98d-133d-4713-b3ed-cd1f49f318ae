<x-app-layout>
<x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('<PERSON><PERSON><PERSON> klant') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
              <form method="POST" action="{{ route('customer.save') }}" class="w-full max-w-xl">
                @csrf
                <div class="flex items-center mb-6">
                  <div class="w-1/3">
                    <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4" for="gln" value="{{ __('GLN') }}" />
                  </div>
                  <div class="w-2/3">
                    <x-input id="gln" class="block mt-1 w-full" type="text" name="gln" value="{{ old('gln') }}"  />
                    @error('gln')
                      <span class="text-red-500 text-xs">{{ $message }}</span>
                    @enderror
                  </div>
                </div>
                <div class="flex items-center mb-6">
                  <div class="w-1/3">
                    <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4" for="exact_id" value="{{ __('Exact ID') }}" />
                  </div>
                  <div class="w-2/3">
                    <x-input id="exact_id" class="block mt-1 w-full" type="text" name="exact" value="{{ old('exact') }}"  />
                  </div>
                </div>
                <div class="flex items-center mb-6">
                  <div class="w-1/3">
                    <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4" for="name" value="{{ __('Naam') }}" />
                  </div>
                  <div class="w-2/3">
                    <x-input id="name" class="block mt-1 w-full" type="text" name="name" value="{{ old('name') }}"  />
                  </div>
                </div>
                <div class="flex items-center mb-6">
                  <div class="w-1/3">
                    <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4" for="address" value="{{ __('Adres') }}" />
                  </div>
                  <div class="w-2/3">
                    <x-input id="address" class="block mt-1 w-full" type="text" name="address" value="{{ old('address') }}"  />
                  </div>
                </div>
                <div class="flex items-center mb-6">
                  <div class="w-1/3">
                    <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4" for="postal_code" value="{{ __('Postcode') }}" />
                  </div>
                  <div class="w-2/3">
                    <x-input id="postal_code" class="block mt-1 w-full" type="text" name="postal_code" value="{{ old('postal_code') }}"  />
                  </div>
                </div>
                <div class="flex items-center mb-6">
                  <div class="w-1/3">
                    <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4" for="city" value="{{ __('Stad') }}" />
                  </div>
                  <div class="w-2/3">
                    <x-input id="city" class="block mt-1 w-full" type="text" name="city" value="{{ old('city') }}"  />
                  </div>
                </div>
                <div class="flex items-center mb-6">
                  <div class="w-1/3">
                    <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4" for="country" value="{{ __('Land') }}" />
                  </div>
                  <div class="w-2/3">
                    <select name="country">
                      <option value="NL">NL</option>
                      <option value="DE">DE</option>
                      <option value="BE">BE</option>
                    </select>
                  </div>
                </div>
                <div class="flex items-center mb-6">
                  <div class="w-1/3">
                    <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4" for="email" value="{{ __('Email') }}" />
                  </div>
                  <div class="w-2/3">
                    <x-input id="email" class="block mt-1 w-full" type="text" name="email" value="{{ old('email') }}"  />
                  </div>
                </div>
                <div class="flex items-center mb-6">
                  <div class="w-1/3">

                  </div>
                  <div><x-button>Opslaan</x-button></div>
                </div>
              </form>
            </div>
        </div>
    </div>
</x-app-layout>
