<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ $product->name }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6 flex flex-column">
                <form method="POST" class="w-full max-w-xl" action="{{ route('product.update', ['id' => $product->id]) }}">
                    @csrf
                    <div class="flex items-center mb-6">
                        <div class="w-1/3">
                            <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4" for="name" value="{{ __('Naam') }}" />
                        </div>
                        <div class="w-2/3">
                            <x-input id="name" class="block mt-1 w-full" type="text" name="name" :value="old('name', $product->name)"  />
                        </div>
                    </div>
                    <div class="flex items-center mb-6">
                        <div class="w-1/3">
                            <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4" for="sku" value="{{ __('SKU') }}" />
                        </div>
                        <div class="w-2/3">
                            <x-input id="sku" class="block mt-1 w-full" type="text" name="sku" :value="old('sku', $product->sku)"  />
                        </div>
                    </div>
                    <div class="flex items-start mb-6">
                        <div class="w-1/3">
                            <x-label class="block text-gray-500 font-bold md:text-right mt-2 mb-1 md:mb-0 pr-4" for="description" value="{{ __('Omschrijving') }}" />
                        </div>
                        <div class="w-2/3">
                            <x-textarea id="description" class="block mt-1 w-full" type="text" name="description" :value="old('description', $product->description)"  />
                        </div>
                    </div>
{{--                    <div class="flex items-center mb-6">--}}
{{--                        <div class="w-1/3">--}}
{{--                            <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4" for="cost" value="{{ __('Kostprijs') }}" />--}}
{{--                        </div>--}}
{{--                        <div class="w-2/3">--}}
{{--                            <x-input id="cost" class="block mt-1 w-full" type="text" name="cost" :value="old('cost', $product->cost)"  />--}}
{{--                        </div>--}}
{{--                    </div>--}}
{{--                    <div class="flex items-center mb-6">--}}
{{--                        <div class="w-1/3">--}}
{{--                            <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4" for="price" value="{{ __('Prijs') }}" />--}}
{{--                        </div>--}}
{{--                        <div class="w-2/3">--}}
{{--                            <x-input id="price" class="block mt-1 w-full" type="text" name="price" :value="old('price', $product->price)"  />--}}
{{--                        </div>--}}
{{--                    </div>--}}
                    <div class="flex items-center mb-6">
                        <div class="w-1/3">
                            <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4" for="gtin" value="{{ __('GTIN') }}" />
                        </div>
                        <div class="w-2/3">
                            <x-input id="gtin" class="block mt-1 w-full" type="text" name="gtin" :value="old('gtin', $product->gtin)"  />
                        </div>
                    </div>
                    <div class="flex items-center mb-6">
                        <div class="w-1/3">
                            <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4" for="transport_qty" value="{{ __('Transport Eenheid Aantal') }}" />
                        </div>
                        <div class="w-2/3">
                            <x-input id="transport_qty" class="block mt-1 w-full" type="text" name="transport_qty" :value="old('transport_qty', $product->transport_qty)"  />
                            <small><i>Hoeveel gaan er op een pallet?</i></small>
                        </div>
                    </div>
                    <div class="flex items-center mb-6">
                        <div class="w-1/3">
                        </div>
                        <div class="w-2/3">
                            <x-button>
                                {{ __('Opslaan') }}
                            </x-button>
                        </div>
                    </div>
                </form>
                <x-section-border />
                <div x-data="{ tab: '{{ request()->input('taskcompleted') == '1' ? 'second' : 'first' }}' }">
                    <nav>
                        <div class="flex-row">
{{--                            <button :class="{ 'tabActive bg-gray-400 button-tab-active': tab === 'first', 'bg-gray-200': tab !== 'first' }" class="rounded px-4 py-2 mr-2 relative  button-tab" @click="tab = 'first'">Voorraad</button>--}}
                            <button :class="{ 'tabActive bg-gray-400 button-tab-active': tab === 'first', 'bg-gray-200': tab !== 'first' }" class="rounded px-4 py-2 mr-2 relative  button-tab" @click="tab = 'first'">Transmission Data</button>
                            <button :class="{ 'tabActive bg-gray-400 button-tab-active': tab === 'third', 'bg-gray-200': tab !== 'third' }" class="rounded px-4 py-2 mr-2 relative  button-tab" @click="tab = 'third'">Kosten opbouw</button>
{{--                            <button :class="{ 'tabActive bg-gray-400 button-tab-active': tab === 'fourth', 'bg-gray-200': tab !== 'fourth' }" class="rounded px-4 py-2 mr-2 relative  button-tab" @click="tab = 'fourth'">Voorraad product</button>--}}
{{--                            <button :class="{ 'tabActive bg-gray-400 button-tab-active': tab === 'fifth', 'bg-gray-200': tab !== 'fifth' }" class="rounded px-4 py-2 mr-2 relative  button-tab" @click="tab = 'fifth'">WooCommerce SKU's</button>--}}
                        </div>
                    </nav>
                    <x-section-border />
                    <div>
                        <div x-show="tab === 'first'">
                            <livewire:transmission-data :productId="$product->id"/>
                        </div>

                        <div x-show="tab === 'third'">
                            <livewire:product-base-product :productId="$product->id"/>
                        </div>

                        <div x-show="tab === 'fourth'">
                            <livewire:storage-product :productId="$product->id"/>
                        </div>

{{--                        <div x-show="tab === 'fifth'">--}}
{{--                            <livewire:woocommerce :productId="$product->id"/>--}}
{{--                        </div>--}}

                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
