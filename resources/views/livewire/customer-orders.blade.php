<div>
  <table id="orders-table" class="table" style="width: 100%"></table>

  @push('scripts')
    <script src="https://code.jquery.com/jquery-3.5.1.js"></script>
    <script src="https://cdn.datatables.net/1.10.23/js/jquery.dataTables.min.js"></script>
    <script>
      document.addEventListener('livewire:init', function () {
        var table = $('#orders-table').DataTable({
          processing: true,
          serverSide: true,
          ajax: '{{ route('livewire.customer-orders.get-orders', ['customerId' => $customerId]) }}',
          columns: [
            { data: 'id', name: 'id', width: '40px' },
            {
              data: 'order_date',
              name: 'order_date',
              width: '100px',
              render: function (data, type, row) {
                if (type === 'display' || type === 'filter') {
                  var date = new Date(data);
                  var day = ("0" + date.getDate()).slice(-2);
                  var month = ("0" + (date.getMonth() + 1)).slice(-2);
                  var year = date.getFullYear();

                  return day + '-' + month + '-' + year;
                }
                return data;
              }
            },
            { data: 'status', name: 'status', width: '100px'},
            { data: 'customer.name', name: 'customer.name' },
            {
              data: null,
              name: 'address.company_name',
              render: function (data, type, row) {
                if (row.address.company_name && row.address.company_name.length > 0) {
                  return row.address.company_name;
                } else {
                  return row.address.name;
                }
              }
            },
            {
              data: 'hasShipment',
              name: 'hasShipment',
              width: '60px',
              className: 'text-center',
              render: function (data, type, row) {
                return data ? '<i class="fa-solid fa-circle-check" style="color: #41d24b;"></i>' : '<i class="fa-solid fa-circle-xmark" style="color: #ea3939;"></i>';
              }
            },
            {
              data: 'delivery_date',
              name: 'delivery_date',
              width: '100px',
              render: function (data, type, row) {
                var date = new Date(data);
                var day = ("0" + date.getDate()).slice(-2);
                var month = ("0" + (date.getMonth() + 1)).slice(-2); // getMonth() is zero-based
                var year = date.getFullYear();

                return day + '-' + month + '-' + year;
              }
            },
            {
              data: 'total_incl_vat',
              name: 'total_incl_vat',
              render: function (data, type, row) {
                return '€ ' + parseFloat(data).toFixed(2);
              }
            },
          ],
          columnDefs: [
            { title: "ID", targets: 0 },
            { title: "Besteldatum", targets: 1 },
            { title: "Status", targets: 2 },
            { title: "Klant", targets: 3 },
            { title: "Eindklant", targets: 4 },
            { title: "TM <i class='fas fa-info-circle'></i>", targets: 5 },
            { title: "Leverdatum", targets: 6 },
            { title: "Prijs", targets: 7 },
          ],
          order: [[0, 'desc']],
          rowCallback: function(row, data) {
            $(row).on('click', function() {
              window.location.href = '/order/edit/' + data.id;
            });
          }
        });
        table.columns().every(function(index) {
          var colheader = $(table.column(index).header());

          if (index === 5) {
            colheader.attr('title', 'Is deze bestelling al aangemeld bij Transmission.');
          }
        });

      });
    </script>
  @endpush
</div>
