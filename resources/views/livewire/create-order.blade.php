<div>
    <div class="flex justify-content-end" style="border-top: 2px solid #000;">
        <div class="flex-column pr-4 pt-4" style="border-right: 2px solid #000">
          <h5>Kost<PERSON>rijs</h5>
          <div style="width: 400px; margin-left: auto" class="flex justify-between">
            <span>Subtotaal:</span><span>&euro; {{ $subTotalCost }}</span>
          </div>
          <div style="width: 400px; margin-left: auto" class="flex justify-between">
            <span>Transport:</span><span><small>(Laadmeters: {{ $transportLength }})</small> &euro; {{ $transportCost }}</span>
          </div>
          <div style="width: 400px; margin-left: auto" class="flex justify-between">
            <span>Totaal:</span><span>&euro; {{ $totalCost }}</span>
          </div>
        </div>
        <div class="flex-column ml-4 pt-4">
          <h5>Klantprijs</h5>
          <div style="width: 400px; margin-left: auto" class="flex justify-between">
            <span>Subtotaal:</span><span>&euro; {{ $subTotal }}</span>
          </div>
          <div style="width: 400px; margin-left: auto;" >
            <div class="flex justify-between">
              <span>Transport: @if($transportPriceTier)
                  <small>Staffel: {{ $transportPriceTierQty }} - &euro; {{ $transportPriceTierPrice }}</small>
                @endif</span><span>&euro; {{ $transport }}</span>
            </div>

          </div>
          <div style="width: 400px; margin-left: auto" class="flex justify-between">
            <span>Totaal:</span><span>&euro; {{ $total }}</span>
          </div>

        </div>
    </div>
    <div class="flex justify-content-end mt-6">
        <x-button wire:click="saveOrder">Bestelling opslaan</x-button>
    </div>

    <x-dialog-modal wire:model.live="showSavingModal">
        <x-slot name="title">
            Bezig met opslaan
        </x-slot>

        <x-slot name="content">
            De bestelling wordt opgeslagen...
        </x-slot>

        <x-slot name="footer">

        </x-slot>
    </x-dialog-modal>
    <div class="absolute inset-0 bg-gray-500 opacity-75 z-50"
         style="display:none;"
         id="overlay"
         wire:loading.attr="style">
    <span class="text-white opacity-75 top-1/2 my-0 mx-auto block relative w-0 h-0" style="top: 50%;">
        <i class="fas fa-circle-notch fa-spin fa-5x"></i>
    </span>
    </div>
</div>
