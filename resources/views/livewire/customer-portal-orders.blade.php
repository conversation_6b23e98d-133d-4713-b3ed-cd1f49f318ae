<div>
  <x-button wire:click="newOrder">Nieuwe bestelling</x-button>
  <table id="orders-table" class="table" style="width: 100%"></table>

  @push('scripts')
    <script src="https://code.jquery.com/jquery-3.5.1.js"></script>
    <script src="https://cdn.datatables.net/1.10.23/js/jquery.dataTables.min.js"></script>
    <script>
      document.addEventListener('livewire:init', function () {
        var table = $('#orders-table').DataTable({
          processing: true,
          serverSide: true,
          ajax: '{{ route('livewire.customer-portal-orders.get-orders') }}',
          columns: [
            { data: 'id', name: 'id', width: '40px' },
            {
              data: 'order_date',
              name: 'order_date',
              width: '100px',
              render: function (data, type, row) {
                if (type === 'display' || type === 'filter') {
                  var date = new Date(data);
                  var day = ("0" + date.getDate()).slice(-2);
                  var month = ("0" + (date.getMonth() + 1)).slice(-2);
                  var year = date.getFullYear();

                  return day + '-' + month + '-' + year;
                }
                return data;
              }
            },
            { data: 'reference', name: 'reference', width: '100px'},
            { data: 'status', name: 'status', width: '100px'},
            {
              data: null,
              name: 'address.company_name',
              render: function (data, type, row) {
                if (row.address.company_name && row.address.company_name.length > 0) {
                  return row.address.company_name;
                } else {
                  return row.address.name;
                }
              }
            },
            {
              data: 'delivery_date',
              name: 'delivery_date',
              width: '100px',
              render: function (data, type, row) {
                var date = new Date(data);
                var day = ("0" + date.getDate()).slice(-2);
                var month = ("0" + (date.getMonth() + 1)).slice(-2); // getMonth() is zero-based
                var year = date.getFullYear();

                return day + '-' + month + '-' + year;
              }
            },

          ],
          columnDefs: [
            { title: "ID", targets: 0 },
            { title: "Besteldatum", targets: 1 },
            { title: "Referentie", targets: 2 },
            { title: "Status", targets: 3 },
            { title: "Eindklant", targets: 4 },
            { title: "Leverdatum", targets: 5 },
          ],
          order: [[0, 'desc']],
          rowCallback: function(row, data) {
            $(row).on('click', function() {
              window.location.href = '/bestelling/' + data.id;
            });
          }
        });

      });
    </script>
  @endpush
</div>
