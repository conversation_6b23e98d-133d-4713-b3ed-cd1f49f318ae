<div>
  <div class="flex">
    <h5 class="mr-3">Log</h5>
    <x-info-balloon>
      <h4>Bestelling Log</h4>
      <p>Hier wordt bijgehouden welke wijzigingen zijn toegepast op een bestelling en door wie deze is gedaan.</p>
      <p>Staat er een oogje voor een log dan kun je hier op klikken en zul je zien wat de wijziging is geweest.</p>
      <p>Bij een "i" icoon staat enkel de actie die is uitgevoerd.</p>
    </x-info-balloon>
  </div>

    @foreach($activities as $activity)
        <div class="flex mb-3 align-items-center">
          @if ($activity->event)
            <i class="fa-solid fa-eye mr-2" wire:click="showDetails({{ $activity->id }})"></i>
          @else
            <i class="fa-solid fa-circle-info mr-2"></i>
          @endif
            <div>
              {{ date('d-m-Y H:i:s' , strtotime($activity->created_at)) }}: {{ class_basename($activity->subject_type) }} {{ $activity->description }} door {{ $activity->causer->name ?? 'System process' }}
            </div>
        </div>
    @endforeach

        <x-dialog-modal wire:model.live="selectedActivity">
            <x-slot name="title">
                Activity Details
            </x-slot>

            <x-slot name="content">
                @if($selectedActivity)
                    @if(isset($selectedActivity->changes['old']))
                    Oud:
                    <ul>
                        @foreach($selectedActivity->changes['old'] as $key => $value)
                            @if ($key !== 'created_at' && $key !== 'updated_at')
                            <li>
                                @if(in_array($key, $this->getChangedAttributes()))
                                <span class="text-red-500"><strong> {{ $key }}:
                                    @if ($key === 'delivery_date')
                                      {{ date('d-m-Y', strtotime($value)) }}
                                    @else
                                      {{ $value }}
                                    @endif

                                  </strong></span>
                                @else
                                  {{ $key }}:
                                    @if ($key === 'delivery_date')
                                      {{ date('d-m-Y', strtotime($value)) }}
                                    @else
                                      {{ $value }}
                                    @endif
                                @endif
                            </li>
                            @endif
                        @endforeach
                    </ul>
                @endif
                    Nieuw:
                    <ul>
                        @foreach($selectedActivity->changes['attributes'] as $key => $value)
                            @if ($key !== 'created_at' && $key !== 'updated_at')
                            <li>
                                @if(in_array($key, $this->getChangedAttributes()))
                                    <span class="text-green-600"><strong>{{ $key }}:
                                        @if ($key === 'delivery_date')
                                          {{ date('d-m-Y', strtotime($value)) }}
                                        @else
                                          {{ $value }}
                                        @endif
                                      </strong></span>
                                @else
                                  {{ $key }}:
                                    @if ($key === 'delivery_date')
                                      {{ date('d-m-Y', strtotime($value)) }}
                                    @else
                                      {{ $value }}
                                    @endif
                                @endif
                            </li>
                            @endif
                        @endforeach
                    </ul>
                @endif
            </x-slot>

            <x-slot name="footer">
                <x-secondary-button wire:click="closeDetails()" wire:loading.attr="disabled">
                    Close
                </x-secondary-button>
            </x-slot>
        </x-dialog-modal>
</div>
