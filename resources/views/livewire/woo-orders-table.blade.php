<div>
  <table id="orders-table" class="table"></table>

  @push('scripts')
    <script src="https://code.jquery.com/jquery-3.5.1.js"></script>
    <script src="https://cdn.datatables.net/1.10.23/js/jquery.dataTables.min.js"></script>
    <script>
      document.addEventListener('livewire:init', function () {
        var table = $('#orders-table').DataTable({
          processing: true,
          serverSide: true,
          stateSave: true,
          ajax: '{{ route('livewire.order-woo-table.get-orders') }}',
          columns: [
            { data: 'id', name: 'id', width: '40px' },
            { data: 'order_id', name: 'order_id', width: '100px'},
            { data: 'sku', name: 'sku' },
            { data: 'qty', name: 'qty' },
            {
              data: 'parsed',
              name: 'parsed',
              width: '50px',
              className: 'text-center',
              render: function (data, type, row) {
                return data ? '<i class="fa-solid fa-circle-check" style="color: #41d24b;"></i>' : '<i class="fa-solid fa-circle-xmark" style="color: #ea3939;"></i>';
              }
            },
            {
              data: 'created_at',
              name: 'created_at',
              width: '100px',
              render: function (data, type, row) {
                var date = new Date(data);

                var day = ("0" + date.getDate()).slice(-2);
                var month = ("0" + (date.getMonth() + 1)).slice(-2); // getMonth() is zero-based
                var year = date.getFullYear();

                return day + '-' + month + '-' + year;
              }
            },
          ],
          columnDefs: [
            { title: "ID", targets: 0 },
            { title: "Order ID", targets: 1 },
            { title: "SKU", targets: 2 },
            { title: "Aantal", targets: 3 },
            { title: "Verwerkt", targets: 4 },
            { title: "Datum", targets: 5 },
          ],
          order: [[0, 'desc']],
        });

      });
    </script>
  @endpush
</div>
