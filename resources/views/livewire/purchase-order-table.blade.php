<div>
  <table id="purchase-orders-table" class="table"></table>

  @push('scripts')
    <script src="https://code.jquery.com/jquery-3.5.1.js"></script>
    <script src="https://cdn.datatables.net/1.10.23/js/jquery.dataTables.min.js"></script>
    <script>
      document.addEventListener('livewire:init', function () {
        var table = $('#purchase-orders-table').DataTable({
          processing: true,
          serverSide: true,
          stateSave: true,
          ajax: '{{ route('livewire.purchase-order-table.get-purchase-orders') }}',
          columns: [
            { data: 'id', name: 'id', width: '70px' },
            {
              data: 'created_at',
              name: 'created_at',
              width: '100px',
              render: function (data, type, row) {
                if (type === 'display' || type === 'filter') {
                  var date = new Date(data);
                  var day = ("0" + date.getDate()).slice(-2);
                  var month = ("0" + (date.getMonth() + 1)).slice(-2);
                  var year = date.getFullYear();

                  return day + '-' + month + '-' + year;
                }
                return data;
              }
            },
            {
              data: 'delivery_date',
              name: 'delivery_date',
              width: '100px',
              render: function (data, type, row) {
                var date = new Date(data);

                var day = ("0" + date.getDate()).slice(-2);
                var month = ("0" + (date.getMonth() + 1)).slice(-2); // getMonth() is zero-based
                var year = date.getFullYear();

                return day + '-' + month + '-' + year;
              }
            },
            { data: 'status', name: 'status', width: '100px'},
            { data: 'customer.name', name: 'customer.name' },
            { data: 'products', name: 'products' },
          ],
          columnDefs: [
            { title: "ID", targets: 0 },
            { title: "Besteldatum", targets: 1 },
            { title: "Leverdatum", targets: 2 },
            { title: "Status", targets: 3 },
            { title: "Leverancier", targets: 4 },
            { title: "Product(en)", targets: 5 },
          ],
          order: [[0, 'desc']],
          rowCallback: function(row, data) {
            $(row).on('click', function() {
              // Redirect to the edit page using the 'id' of the clicked row
              window.location.href = '/purchaseorder/edit/' + data.id;
            });
          }
        });
      });
    </script>
  @endpush
</div>
