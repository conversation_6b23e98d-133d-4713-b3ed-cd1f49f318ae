<div>
    <x-button wire:click="$set('showNewAddressModal', true)"><PERSON>res toevoegen</x-button>
    <div class="w-full mt-2 flex gap-4 flex-wrap">
      @foreach($addresses as $address)
        <div class="rounded bg-gray-200 p-4 w-48 relative">
          {{ $address->company_name }}<br>
          {{ $address->name }}<br>
          {{ $address->street }} {{ $address->housenumber }}<br>
          {{ $address->postal_code }} {{ $address->city }}<br>
          @if($address->gln)
            GLN: {{ $address->gln }}
          @endif
          <i class="fa-regular fa-pen-to-square absolute cursor-pointer" style="top: 10px; right: 10px;" wire:click="editAddress({{ $address->id }})"></i>
        </div>
      @endforeach
    </div>

    <x-dialog-modal wire:model.live="showEditAddressModal">
        <x-slot name="title">
            Adres aanpassen
        </x-slot>

        <x-slot name="content">
            <div class="mb-4">
                <x-label for="company_name" value="Bedrijfsnaam" />
                <x-input id="company_name" class="block mt-1 w-full" type="text" wire:model.live="editCompanyName" autofocus />
            </div>
            <div class="mb-4">
                <x-label for="name" value="Naam" />
                <x-input id="name" class="block mt-1 w-full" type="text" wire:model.live="editName" />
            </div>
            <div class="mb-4">
                <x-label for="street_name" value="Straatnaam + huisnummer" />
                <div class="flex gap-4">
                    <x-input id="street_name" class="block mt-1 w-full" type="text" wire:model.live="editStreetName" />
                    <x-input id="housenumber" class="block mt-1 w-25" type="text" wire:model.live="editHousenumber" />
                </div>
            </div>
            <div class="mb-4">
                <x-label for="postalcode" value="Postcode + Plaats" />
                <div class="flex gap-4">
                    <x-input id="postalcode" class="block mt-1 w-25" type="text" wire:model.live="editPostalcode" />
                    <x-input id="city" class="block mt-1 w-full" type="text" wire:model.live="editCity" />
                </div>
            </div>
            <div class="mb-4">
                <x-label for="gln" value="GLN (optioneel)" />
                <x-input id="gln" class="block mt-1 w-full" type="text" wire:model.live="editGln" />
            </div>
        </x-slot>

        <x-slot name="footer">
            <x-secondary-button wire:click="$set('showEditAddressModal', false)" wire:loading.attr="disabled">
                Annuleren
            </x-secondary-button>

            <x-button class="ml-2" wire:click.prevent="updateAddress" wire:loading.attr="disabled">
                Opslaan
            </x-button>
        </x-slot>
    </x-dialog-modal>

    <x-dialog-modal wire:model.live="showNewAddressModal">
        <x-slot name="title">
            Adres toevoegen
        </x-slot>

        <x-slot name="content">
            <div class="mb-4">
                <x-label for="company_name" value="Bedrijfsnaam" />
                <x-input id="company_name" class="block mt-1 w-full" type="text" wire:model.live="companyName" autofocus />
            </div>
            <div class="mb-4">
                <x-label for="name" value="Naam" />
                <x-input id="name" class="block mt-1 w-full" type="text" wire:model.live="name" />
            </div>
            <div class="mb-4">
                <x-label for="street_name" value="Straatnaam + huisnummer" />
                <div class="flex gap-4">
                    <x-input id="street_name" class="block mt-1 w-full" type="text" wire:model.live="streetName" />
                    <x-input id="housenumber" class="block mt-1 w-25" type="text" wire:model.live="housenumber" />
                </div>
            </div>
            <div class="mb-4">
                <x-label for="postalcode" value="Postcode + Plaats" />
                <div class="flex gap-4">
                    <x-input id="postalcode" class="block mt-1 w-25" type="text" wire:model.live="postalcode" />
                    <x-input id="city" class="block mt-1 w-full" type="text" wire:model.live="city" />
                </div>
            </div>
            <div class="mb-4">
                <x-label for="gln" value="GLN (optioneel)" />
                <x-input id="gln" class="block mt-1 w-full" type="text" wire:model.live="gln" />
            </div>
        </x-slot>

        <x-slot name="footer">
            <x-secondary-button wire:click="$set('showNewAddressModal', false)" wire:loading.attr="disabled">
                Annuleren
            </x-secondary-button>

            <x-button class="ml-2" wire:click.prevent="addAddress" wire:loading.attr="disabled">
                Opslaan
            </x-button>
        </x-slot>
    </x-dialog-modal>
</div>
