<div class="mb-4">
  <input
          type="text"
          wire:model.live.debounce.500ms="search"
          placeholder="<PERSON><PERSON> klant..."
          class="@if(!empty($selectedCustomer)) hidden @endif"
  />
  @unless($customers->isEmpty())
    <div class="bg-gray-100" style="max-height: 400px; overflow-x: scroll; border: 1px solid #4b5563">
      @foreach($customers as $customer)
        <p
                wire:click="selectCustomer({{ $customer->id }})"
                class="cursor-pointer hover:bg-gray-50 py-2 px-2"
                style="margin-bottom: 0px !important;"
        >
          {{ $customer->name }}
        </p>
      @endforeach
    </div>
  @endunless

  @if(!empty($selectedCustomer))
    <div id="billing">
      {{ $selectedCustomer->name }}<br>
      <span wire:click="$set('showModal', true)" class="cursor-pointer">T.a.v. {{ $customerName }}</span><br>
      {{ $selectedCustomer->address }}<br>
      {{ $selectedCustomer->postal_code }} {{ $selectedCustomer->city }}<br>
      <span wire:click="$set('showModal', true)" class="cursor-pointer">{{ $customerEmail }}</span>
    </div>
  @endif
  <x-dialog-modal wire:model.live="showModal">
    <x-slot name="title">
      Ter attentie van
    </x-slot>
    <x-slot name="content">
      <div class="mb-4">
        <x-label for="customerName" value="Naam" />
        <x-input id="customerName" class="block mt-1 w-full" type="text" wire:model.live="customerName" autofocus />
      </div>
      <div class="mb-4">
        <x-label for="customerEmail" value="Email adres" />
        <x-input id="customerEmail" class="block mt-1 w-full" type="text" wire:model.live="customerEmail" />
      </div>
    </x-slot>
    <x-slot name="footer">
        <x-button wire:click="saveCustomer">Ok</x-button>
    </x-slot>
  </x-dialog-modal>
</div>
