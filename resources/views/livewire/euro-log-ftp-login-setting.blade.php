<div>
  <div class="mb-4">
    <x-label for="ftp" value="FTP" />
    <x-input id="ftp" class="block mt-1 w-full" type="text" wire:model.live="ftp" />
  </div>
  <div class="mb-4">
    <x-label for="username" value="Username" />
    <x-input id="username" class="block mt-1 w-full" type="text" wire:model.live="username" />
  </div>
  <div class="mb-4">
    <x-label for="password" value="Wacthwoord" />
    <x-input id="password" class="block mt-1 w-full" type="password" wire:model.live="password" />
  </div>
  <x-button wire:click.prevent="saveCredentials" wire:loading.attr="disabled">
    Opslaan
  </x-button>
</div>
