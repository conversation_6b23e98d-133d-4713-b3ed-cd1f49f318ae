<div>
    <table style="width: 100%">
        <thead>
        <tr  style="border-bottom: 2px solid #000; height: 45px;">
            <th style="width: 5%">Nr</th>
            <th style="width: 20%">SKU</th>
            <th style="width: 40%">Product</th>
            <th style="width: 10%">Aantal</th>
            <th style="width: 10%"><PERSON><PERSON><PERSON>rijs</th>
            <th style="width: 10%">Prijs</th>
            <th style="width: 10%"></th>
        </tr>
        </thead>
        <tbody>
    @foreach($order->orderitems as $index => $orderItem)
        <tr @if(in_array($orderItem->id, $removedOrderlines))class="text-decoration-line-through" @endif  style="border-bottom: 1px solid #ccc; height: 45px;">
            <td>{{ $index + 1 }}</td>
            <td>{{ $orderItem['sku'] }}</td>
            <td>{{ $orderItem['product_name'] }} <br> <span style="{{ str_contains($orderItem['calculation'], 'Geen') ? 'color: red;' : '' }}">
    <small>{{ $orderItem['calculation'] }}</small>
</span></td>
            <td>{{ $orderItem['qty'] }}</td>
            <td>&euro; {{ $orderItem['cost'] }}</td>
            <td>&euro; {{ $orderItem['price_ex_vat'] }}</td>
            <td>
                @if(!$order->hasShipment && $order->status != 'COMPLETE')
                    <i class="fa-solid fa-pen-to-square mr-2" wire:click="editOrderline({{$orderItem['id']}})"></i>
                    @if(!in_array($orderItem->id, $removedOrderlines))<i class="fa-solid fa-trash" wire:click="removeOrderitem({{$orderItem->id}})" style="color: #dd2222;"></i>
                    @else
                        <i class="fa-solid fa-trash-can-arrow-up" style="color: #3bd855;" wire:click="restoreOrderitem({{$orderItem->id}})"></i>
                    @endif
                @endif
            </td>
        </tr>
        @if($orderItem->product->storageProduct)
            @php
                $storageProduct = $orderItem->product->storageProduct;
            @endphp
            <tr style="border-bottom: 1px solid #ccc; height: 45px;" class="text-sm">
                <td><i class="fa-sharp fa-solid fa-arrow-turn-up" style="transform: rotate(90deg); margin-left: 10px;"></i></td>
                <td>{{ $storageProduct->sku }}</td>
                <td>{{ $storageProduct->name }}</td>
                <td>{{ $orderItem['qty'] * $orderItem->product->storage_product_qty }}</td>
                <td></td>
                <td></td>
                <td></td>
            </tr>
        @endif
    @endforeach
        </tbody>
    </table>
    <x-dialog-modal wire:model.live="editingOrderlineId">
        <x-slot name="title">
           Bewerk orderregel
        </x-slot>

        <x-slot name="content">
            <h5>{{ $sku }} {{ $name }}</h5>
            <div class="mb-4">
                <x-label for="qty" value="Aantal" />
                <x-input id="qty" class="block mt-1 w-full" type="text" wire:model.live="qty" />
            </div>
            <div class="mb-4">
                <x-label for="price" value="Prijs" />
                <x-input id="price" class="block mt-1 w-full" type="text" wire:model.live="price_ex_vat" />
            </div>
        </x-slot>

        <x-slot name="footer">
            <x-secondary-button wire:click="$toggle('editingOrderlineId')" wire:loading.attr="disabled">
                Annuleren
            </x-secondary-button>

            <x-button class="ml-2" wire:click="updateOrderline" wire:loading.attr="disabled">
                Opslaan
            </x-button>
        </x-slot>
    </x-dialog-modal>
</div>
