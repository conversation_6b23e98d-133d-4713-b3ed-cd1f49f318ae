<div>
  <strong>Verzending</strong><br>
  @if($order->status != 'CANCELLED')
  @if($order->shipper === 'pickup')
    <p>Geen verzending.</p>
      <a class="text-decoration-none mt-2 inline-flex items-center px-4 py-2 bg-white border border-gray-300 rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-25 transition ease-in-out duration-150"
         href="{{ route('order.print', $order->id) }}" target="_blank">Print Pakbon</a>
  @endif
  @if ($order->shipment)
    <x-secondary-button wire:click.prevent="printLabel" wire:loading.attr="disabled">
      Print Label
    </x-secondary-button><br>
    <x-secondary-button wire:click.prevent="getShipmentStatus" wire:loading.attr="disabled" class="mt-2">
      Status
    </x-secondary-button><br>
    <a class="text-decoration-none mt-2 inline-flex items-center px-4 py-2 bg-white border border-gray-300 rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-25 transition ease-in-out duration-150"
            href="{{ $order->shipment->track_trace }}" target="_blank">Track&Trace</a><br>

    <a class="text-decoration-none mt-2 inline-flex items-center px-4 py-2 bg-white border border-gray-300 rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-25 transition ease-in-out duration-150"
            href="{{ route('order.print', $order->id) }}" target="_blank">Print Pakbon</a>

    <x-dialog-modal wire:model="showShipmentStatusModal">
      <x-slot name="title">
        Verzending log
      </x-slot>

      <x-slot name="content">
        @if(is_array($shipmentStatus))
          @foreach($shipmentStatus as $status)
            <div>
              {{ $status['status_date'] }} {{ $status['status_time'] }} - {{ $status['status_description'] }}
            </div>
          @endforeach
        @endif
      </x-slot>

      <x-slot name="footer">
        <x-secondary-button wire:click="$set('showShipmentStatusModal', false)" wire:loading.attr="disabled">
          Sluiten
        </x-secondary-button>
      </x-slot>
    </x-dialog-modal>

    <x-dialog-modal wire:model="showTestModal" maxWidth="6xl">
      <x-slot name="title">
        Test Transmission - Request & Response Data
      </x-slot>

      <x-slot name="content">
        <div class="space-y-6">
          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-3">Request Data (Sent to API)</h3>
            <div class="bg-gray-100 p-4 rounded-lg overflow-auto max-h-96">
              <pre class="text-sm text-gray-800">{{ json_encode($testRequestData, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES) }}</pre>
            </div>
          </div>

          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-3">Response Data (Received from API)</h3>
            <div class="bg-gray-100 p-4 rounded-lg overflow-auto max-h-96">
              <pre class="text-sm text-gray-800">{{ json_encode($testResponseData, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES) }}</pre>
            </div>
          </div>
        </div>
      </x-slot>

      <x-slot name="footer">
        <x-secondary-button wire:click="$set('showTestModal', false)" wire:loading.attr="disabled">
          Sluiten
        </x-secondary-button>
      </x-slot>
    </x-dialog-modal>
  @else
    @if($order->shipper == 'transmission')
      <x-button wire:click.prevent="shipOrder" wire:loading.attr="disabled" :disabled="$isShipped">
        Transmission
      </x-button>
      <x-secondary-button wire:click.prevent="testTransmission" wire:loading.attr="disabled" class="ml-2">
        Test Transmission
      </x-secondary-button>
        <div>
          <x-label>Afleverinstructie</x-label>
          <textarea rows="4" wire:model.live="instruction" class="peer h-full min-h-[50px] w-full resize-none rounded-[7px] border border-blue-gray-200 border-t-transparent bg-transparent px-3 font-sans text-sm font-normal text-blue-gray-700 outline outline-0 transition-all placeholder-shown:border placeholder-shown:border-blue-gray-200 placeholder-shown:border-t-blue-gray-200 focus:border-2 focus:border-pink-500 focus:border-t-transparent focus:outline-0 disabled:resize-none disabled:border-0 disabled:bg-blue-gray-50"></textarea>
        </div>
        <label class="toggle">
          <input class="toggle-checkbox" type="checkbox" wire:model.live="exchangeUnit">
          <div class="toggle-switch"></div>
          <span class="toggle-label">Omruilen pallet</span>
        </label>
    @else
{{--      @if($order->shipper != 'pickup')--}}
{{--        <x-button wire:click.prevent="addTransmissionCost">Transmission kosten toevoegen</x-button>--}}
{{--      @endif--}}
    @endif
  @endif
    @endif
</div>
