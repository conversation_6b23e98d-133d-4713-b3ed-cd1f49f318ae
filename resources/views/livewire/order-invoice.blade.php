<div class="w-full">
    <div class="flex justify-between">
        <h5>Factuur ({{ $invoice->id }}) @if($groupedInvoice) GEGROEPEERD @endif</h5>
        @if($invoice->sent == 0 && $invoice->order->customer->id == 10441)
            <x-button wire:click="sendWelkoopInvoice">Verstuur naar Transus</x-button>
        @endif
        @if($invoice->sent == 0 && $invoice->order->customer->id == 10551)
            <x-button wire:click="sendHornbachInvoice">Verstuur naar Transus</x-button>
        @endif
    </div>
    @if($invoice->sent == 1)
        <strong><span style="color: green"><i class="fa-solid fa-check" style="color: #36b234;"></i> Verstuurd</span></strong>
    @endif
    <table>
        <tr>
            <td style="width: 200px">Factuurnummer:</td>
            <td>{{ $invoice->invoice_nr }}</td>
        </tr>
        <tr>
            <td>Factuur datum:</td>
            <td>{{ str_replace('00:00:00', '', $invoice->invoice_date) }}</td>
        </tr>
        <tr>
            <td>Totaal excl BTW:</td>
            <td>&euro; {{ $invoice->net_line_amount }}</td>
        </tr>
        <tr>
            <td>BTW {{ $invoice->vat_prct }}%:</td>
            <td>&euro; {{ $invoice->vat_amount }}</td>
        </tr>
        <tr style="font-weight: bold">
            <td>Totaal incl BTW:</td>
            <td>&euro; {{ $invoice->total }}</td>
        </tr>
    </table>
    <table class="mt-3 table" style="width: 100%">
        <tr>
            <th>SKU</th>
            <th>Aantal</th>
            <th>Prijs per eenheid</th>
            <th>Totaal</th>
        </tr>
        @foreach ($invoice->invoiceitem as $invoiceItem)
            <tr>
                <td>{{ $invoiceItem->sku }}</td>
                <td>{{ $invoiceItem->invoiced_qty }}</td>
                <td>&euro; {{ $invoiceItem->article_net_price }}</td>
                <td>&euro; {{ $invoiceItem->net_line_amount }}</td>
            </tr>
        @endforeach
    </table>
</div>
