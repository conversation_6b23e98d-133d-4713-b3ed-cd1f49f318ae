<div>
  <div class="flex justify-between">
    <div>
        <input type="date" wire:model.live="start_date" class="mr-2">tot
        <input type="date" wire:model.live="end_date" class="ml-1">
        <button style="height: 44px; margin-left: 20px;" wire:click="loadInteractions" class="inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">Toon</button>
    </div>
    <button class="inline-flex items-center px-4 py-2 border bg-success border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150" wire:click="openModal">
      <svg fill="#ffffff" style="width: 12px; margin-right: 8px;" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512">
        <path d="M256 80c0-17.7-14.3-32-32-32s-32 14.3-32 32V224H48c-17.7 0-32 14.3-32 32s14.3 32 32 32H192V432c0 17.7 14.3 32 32 32s32-14.3 32-32V288H400c17.7 0 32-14.3 32-32s-14.3-32-32-32H256V80z"/></svg>
      Toevoegen
    </button>
  </div>

  @if(count($interactions) > 0)
    @foreach($interactions as $interaction)
      <div class="max-w-xl px-6 my-4 py-6 bg-white rounded-lg shadow-md" style="border: 1px solid #d1d5db">
        <div class="flex justify-between items-center">
          <div class="flex">
          @if($interaction->type == 'call')
            <svg style="width: 15px;" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
              <path d="M164.9 24.6c-7.7-18.6-28-28.5-47.4-23.2l-88 24C12.1 30.2 0 46 0 64C0 311.4 200.6 512 448 512c18 0 33.8-12.1 38.6-29.5l24-88c5.3-19.4-4.6-39.7-23.2-47.4l-96-40c-16.3-6.8-35.2-2.1-46.3 11.6L304.7 368C234.3 334.7 177.3 277.7 144 207.3L193.3 167c13.7-11.2 18.4-30 11.6-46.3l-40-96z"/>
            </svg>
          @elseif($interaction->type == 'meeting')
              <svg style="width: 15px;" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512">
                <path d="M224 256A128 128 0 1 0 224 0a128 128 0 1 0 0 256zm-45.7 48C79.8 304 0 383.8 0 482.3C0 498.7 13.3 512 29.7 512H418.3c16.4 0 29.7-13.3 29.7-29.7C448 383.8 368.2 304 269.7 304H178.3z"/>
              </svg>
          @elseif($interaction->type == 'email')
              <svg style="width: 15px;" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                <path d="M48 64C21.5 64 0 85.5 0 112c0 15.1 7.1 29.3 19.2 38.4L236.8 313.6c11.4 8.5 27 8.5 38.4 0L492.8 150.4c12.1-9.1 19.2-23.3 19.2-38.4c0-26.5-21.5-48-48-48H48zM0 176V384c0 35.3 28.7 64 64 64H448c35.3 0 64-28.7 64-64V176L294.4 339.2c-22.8 17.1-54 17.1-76.8 0L0 176z"/>
              </svg>
          @endif
          <span class="font-light text-gray-600 ml-2">{{ $interaction->date }}</span>
          </div>
          <span>{{ $interaction->user->name }}</span>
        </div>
        <div class="mt-2">
          <p class="mt-2 text-gray-600">{{ $interaction->content }}</p>
        </div>
      </div>
    @endforeach
  @else
    Nog geen communicatie geregistreerd bij deze klant.
  @endif

  <x-dialog-modal wire:model.live="showModal">
    <x-slot name="title">
      Add Interaction
    </x-slot>

    <x-slot name="content">
      <div class="flex flex-wrap">
        <div class="w-full mt-4">
          <x-label for="interactionType" value="{{ __('Type') }}" />
          <select id="interactionType" wire:model.live="interactionType" class="mt-1 block w-full">
            <option value="">-- Select Type --</option>
            <option value="call">Telefoon</option>
            <option value="meeting">Gesprek</option>
            <option value="email">Email</option>
          </select>
        </div>
        <div class="w-full mt-4">
          <x-label for="interactionDescription" value="{{ __('Description') }}" />
          <x-input id="interactionDescription" type="text" class="mt-1 block w-full" wire:model.live="interactionDescription" />
        </div>
      </div>
    </x-slot>

    <x-slot name="footer">
      <x-secondary-button wire:click="closeModal" wire:loading.attr="disabled">
        {{ __('Cancel') }}
      </x-secondary-button>

      <x-button class="ml-2" wire:click="createInteraction" wire:loading.attr="disabled">
        {{ __('Save') }}
      </x-button>
    </x-slot>
  </x-dialog-modal>
</div>
