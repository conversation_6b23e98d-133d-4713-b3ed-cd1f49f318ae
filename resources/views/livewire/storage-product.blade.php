<div>
  <h4>Voorraad product</h4>
  @if($isStorageProduct)
    <p>
      Dit product is zelf een voorraad product. Deze kan dus niet zelf ook nog eens een voorraadproduct hebben.<br/>
      De volgende producten hebben dit product als voorraad product:
    </p>
    <ul class="list-disc">
      @foreach($product->associatedProducts as $associatedProduct)
        <li><a href="{{ route('product.edit', $associatedProduct->id) }}">{{ $associatedProduct->sku }} - {{ $associatedProduct->name }}</a></li>
      @endforeach
    </ul>
  @else
      @if(!$selectedStorageProduct)
      <input
              type="text"
              wire:model.live.debounce.500ms="search"
              placeholder="Zoek product..."
      />
      @unless($products->isEmpty())
          <div class="bg-gray-100" style="max-height: 400px; overflow-x: scroll; border: 1px solid #4b5563">
            @foreach($products as $storageProduct)
              <p
                  wire:click="selectProduct({{ $storageProduct->id }})"
                  class="cursor-pointer hover:bg-gray-50 py-2 px-2"
                  style="margin-bottom: 0px !important;"
              >
              {{ $storageProduct->sku }} - {{ $storageProduct->name }}
            </p>
          @endforeach
          </div>
      @endunless
      @else
        <strong>{{ $selectedStorageProduct->sku }} - {{ $selectedStorageProduct->name }} - Aantal voorraad producten per eenheid: {{ $product->storage_product_qty }}</strong><i class="ml-4 fa-regular fa-trash-can cursor-pointer" style="color: #db2814;" wire:click="removeStorageProduct()"></i>
      @endif
  @endif
  <x-dialog-modal wire:model.live="showModal">
    <x-slot name="title">
      Aantal
    </x-slot>

    <x-slot name="content">
      <div class="mb-4">
        <x-label for="aantal" value="Aantal" />
        <x-input id="aantal" class="block mt-1 w-full" type="text" wire:model.live="quantity" autofocus />
      </div>
    </x-slot>

    <x-slot name="footer">
      <x-secondary-button wire:click="$set('showModal', false)" wire:loading.attr="disabled">
        Annuleren
      </x-secondary-button>

      <x-button class="ml-2" wire:click.prevent="saveStorageProduct" wire:loading.attr="disabled">
        Opslaan
      </x-button>
    </x-slot>
  </x-dialog-modal>
</div>
