<div>
  <div class="flex items-center mb-6">
    <div class="w-48">
    </div>
    <div class="w-2/3">
      <label class="toggle">
        <input class="toggle-checkbox" type="checkbox" wire:model.live="byProduct">
        <div class="toggle-switch"></div>
        <span class="toggle-label">Is bijproduct: <strong>@if($product->is_by_product) JA @else NEE @endif </strong></span>
      </label>
    </div>
  </div>
  @if(!$byProduct)
  <div class="flex items-center mb-6">
    <div class="w-48">
      <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4" for="weight" value="{{ __('Gewicht') }}" />
    </div>
    <div class="w-2/3">
      <x-input id="weight" class="block mt-1 w-full" type="text" name="weight" wire:model.live="weight"  />
    </div>
  </div>
  <div class="flex items-center mb-6">
    <div class="w-48">
      <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4" for="length" value="{{ __('Lengte') }}" />
    </div>
    <div class="w-2/3">
      <x-input id="length" class="block mt-1 w-full" type="text" name="length" wire:model.live="length" />
    </div>
  </div>
  <div class="flex items-center mb-6">
    <div class="w-48">
      <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4" for="width" value="{{ __('Breedte') }}" />
    </div>
    <div class="w-2/3">
      <x-input id="width" class="block mt-1 w-full" type="text" name="width" wire:model.live="width"  />
    </div>
  </div>
  <div class="flex items-center mb-6">
    <div class="w-48">
      <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4" for="height" value="{{ __('Hoogte') }}" />
    </div>
    <div class="w-2/3">
      <x-input id="height" class="block mt-1 w-full" type="text" name="height" wire:model.live="height"  />
    </div>
  </div>
  <div class="flex items-center mb-6">
    <div class="w-48">
      <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4" for="description" value="{{ __('Omschrijving') }}" />
    </div>
    <div class="w-2/3">
      <x-input id="description" class="block mt-1 w-full" type="text" name="description" wire:model.live="description"  />
    </div>
  </div>
  <div class="flex items-center mb-6">
    <div class="w-48">
      <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4" for="unit_type" value="{{ __('Unit type') }}" />
    </div>
    <div class="w-2/3">
      <select name="unit_type" wire:model.live="unitType">
        <option value="">Selecteer</option>
        <option value="BP">BLOKPALLET</option>
        <option value="EP">EUROPALLET</option>
      </select>
    </div>
  </div>
  @endif
  <x-button wire:click="saveTransmissionData">Opslaan</x-button>
</div>
