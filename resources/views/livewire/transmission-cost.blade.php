<div>
      <div>
        <table class="table-fixed mb-4">
          <thead class="border-b font-bold">
          <tr>
            <th scope="col" class="py-1" width="120">Lengtemeter</th>
            <th scope="col" class="py-1" width="120">Prijs NL</th>
            <th scope="col" class="py-1" width="120">Prijs BE</th>
            <th scope="col" class="py-1" width="120">Prijs ZV</th>
            <th scope="col" class="py-1" width="120">Prijs WE</th>
            <th scope="col" class="py-1" width="120">Prijs Noord</th>
            <th scope="col" class="py-1" width="150">Datum van</th>
            <th scope="col" class="py-1" width="150">Datum tot</th>
          </tr>
          </thead>
          <tbody>
              @foreach($transmissionCostTiers as $index => $tier)
                <tr class="border-b @if($index % 2 === 0) bg-gray-100 @endif">
                  <td>{{ $tier->trailer_length }}</td>
                  <td>&euro; {{ $tier->price_nl }}</td>
                  <td>&euro; {{ $tier->price_be }}</td>
                  <td>&euro; {{ $tier->price_zv }}</td>
                  <td>&euro; {{ $tier->price_we }}</td>
                  <td>&euro; {{ $tier->price_north }}</td>
                  <td>{{ $tier->from_date?->format('d-m-Y') }}</td>
                  <td>{{ $tier->to_date?->format('d-m-Y') }}</td>
                </tr>
              @endforeach
          </tbody>
        </table>
      </div>

      <x-button wire:click="$set('showModal', true)">Prijsstaffel toevoegen</x-button>


      <x-dialog-modal wire:model.live="showModal">
        <x-slot name="title">
          Nieuwe staffel
        </x-slot>

        <x-slot name="content">
          @if($errorMessage)
            <div class="alert alert-danger">{{ $errorMessage }}</div>
          @endif
          <div class="mb-4">
            <x-label for="trailerLength" value="Vanaf" />
            <x-input id="trailerLength" class="block mt-1 w-full" type="number" wire:model.live="trailerLength" autofocus />
          </div>
          <div class="mb-4">
            <x-label for="priceNL" value="Price NL" />
            <x-input id="priceNL" class="block mt-1 w-full" type="number" wire:model.live="priceNL" />
          </div>
          <div class="mb-4">
            <x-label for="priceBE" value="Price BE" />
            <x-input id="priceBE" class="block mt-1 w-full" type="number" wire:model.live="priceBE" />
          </div>
          <div class="mb-4">
            <x-label for="priceZV" value="Price ZV" />
            <x-input id="priceZV" class="block mt-1 w-full" type="number" wire:model.live="priceZV" />
          </div>
          <div class="mb-4">
            <x-label for="priceWE" value="Price WE" />
            <x-input id="priceWE" class="block mt-1 w-full" type="number" wire:model.live="priceWE" />
          </div>
          <div class="mb-4">
            <x-label for="priceNorth" value="Price Noord" />
            <x-input id="priceNorth" class="block mt-1 w-full" type="number" wire:model.live="priceNorth" />
          </div>
          <div class="mb-4">
            <x-label for="fromDate" value="From Date" />
            <x-input id="fromDate" class="block mt-1 w-full" type="date" wire:model.live="fromDate" />
          </div>
          <div class="mb-4">
            <x-label for="toDate" value="To Date" />
            <x-input id="toDate" class="block mt-1 w-full" type="date" wire:model.live="toDate" />
          </div>
        </x-slot>

        <x-slot name="footer">
          <x-secondary-button wire:click="$set('showModal', false)" wire:loading.attr="disabled">
            Annuleren
          </x-secondary-button>

          <x-button class="ml-2" wire:click.prevent="saveTier" wire:loading.attr="disabled">
            Opslaan
          </x-button>
        </x-slot>
      </x-dialog-modal>
</div>
