<div>
  <div class="flex justify-between">
    <div class="w-1-3">
      <div class="mb-4">
        <x-label for="name" value="Naam" />
        <x-input id="name" class="block mt-1 w-full" type="text" wire:model.live="name" />
      </div>
      <div class="mb-4">
        <x-label for="street" value="Straat + huisnummer" />
        <div class="flex gap-3">
          <x-input id="street" class="block mt-1 w-full" type="text" wire:model.live="street" />
          <x-input id="housenumber" class="block mt-1 w-1/3" type="text" wire:model.live="housenumber" />
        </div>
      </div>
      <div class="mb-4">
        <x-label for="postalcode" value="Postcode + Plaats" />
        <div class="flex gap-3">
          <x-input id="postalcode" class="block mt-1 w-1/3" type="text" wire:model.live="postalCode" />
          <x-input id="city" class="block mt-1 w-full" type="text" wire:model.live="city" />
        </div>
      </div>
      <div class="mb-4">
        <label for="country" class="block font-medium text-sm text-gray-700">Country</label>
        <select id="country" class="form-input rounded-md shadow-sm mt-1 block w-full" wire:model.live="country">
          <option value="">Selecteer Land</option>
          <option value="NL">Nederland</option>
          <option value="BE">Belgie</option>
        </select>
      </div>
      <div class="mb-4">
        <x-label for="email" value="Email" />
        <x-input id="email" class="block mt-1 w-full" type="email" wire:model.live="email" />
      </div>
      <div class="mb-4">
        <x-label for="telephone" value="Telefoon" />
        <x-input id="telephone" class="block mt-1 w-full" type="text" wire:model.live="telephone" />
      </div>
    </div>
    <div>
      <div class="mb-4">
        <x-label for="reference" value="Referentie" />
        <x-input id="reference" class="block mt-1 w-full" type="text" wire:model.live="reference" />
      </div>
      <div class="mb-4">
        <strong>Leverdatum</strong>
        <x-input id="leverdatum" class="block mt-1 w-full" type="date" wire:model.live="deliveryDate" />
      </div>
    </div>
  </div>
  <x-section-border />
  <x-button wire:click="$set('showModal', true)">Product toevoegen</x-button>
    <table style="width: 100%">
      <thead>
      <tr  style="border-bottom: 2px solid #000; height: 45px;">
        <th style="width: 5%">Nr</th>
        <th style="width: 40%">Product</th>
        <th style="width: 10%">Aantal</th>
      </tr>
      </thead>
      <tbody>
      @foreach($orderlines as $index => $orderline)
        <tr  style="border-bottom: 1px solid #ccc; height: 45px;">
          <td>{{ $index + 1 }}</td>
          <td>{{ $orderline['product_name'] }}</td>
          <td>{{ $orderline['quantity'] }}</td>
        </tr>
      @endforeach
      </tbody>
    </table>
  <div class="flex mt-6 justify-content-end">
    <x-button wire:click="saveOrder">Bestelling plaatsen</x-button>
  </div>
  @if($orderError)
    <div class="bg-red-100 p-4 mt-4">
      {{ $orderError }}
    </div>
  @endif


  <x-dialog-modal wire:model.live="showModal">
    <x-slot name="title">
      Product toevoegen
    </x-slot>

    <x-slot name="content">
      <div class="mb-4">
        <div class="@if(!empty($orderlineProduct)) hidden @endif">
          <label for="product" class="block font-medium text-sm text-gray-700">Product</label>
          <select id="product" class="form-input rounded-md shadow-sm mt-1 block w-full" wire:model.live="selectedProduct">
            <option value="">Selecteer Product</option>
            @foreach($products as $product)
              <option value="{{$product->id}}">{{ $product->sku }} - {{ $product->name }}</option>
            @endforeach
          </select>
{{--          <input--}}
{{--                  type="text"--}}
{{--                  wire:model.live.debounce.500ms="search"--}}
{{--                  placeholder="Zoek product..."--}}
{{--          />--}}
{{--          @unless($products->isEmpty())--}}
{{--            <div class="bg-gray-100" style="max-height: 400px; overflow-x: scroll; border: 1px solid #4b5563">--}}
{{--              @foreach($products as $product)--}}
{{--                <p--}}
{{--                        wire:click="selectProduct({{ $product->id }})"--}}
{{--                        class="cursor-pointer hover:bg-gray-50 py-2 px-2"--}}
{{--                        style="margin-bottom: 0px !important;"--}}
{{--                >--}}
{{--                  {{ $product->sku }} - {{ $product->name }}--}}
{{--                </p>--}}
{{--              @endforeach--}}
{{--            </div>--}}
{{--          @endunless--}}
        </div>
        @if(!empty($orderlineProduct))
          {{ $orderlineProduct->name }}
        @endif
      </div>
      <div class="mb-4">
        <x-label for="aantal" value="Aantal" />
        <x-input id="aantal" class="block mt-1 w-full" type="number" wire:model.live="quantity" />
        @if(!empty($orderlineProduct))
          @if ($orderlineProduct->transport_qty)
            <small><strong>LET OP! Aantal per transport eenheid is {{$orderlineProduct->transport_qty}}. Aantal moet dus deelbaar zijn door {{$orderlineProduct->transport_qty}}</strong></small>
          @endif
        @endif
      </div>
      @if($errorMessage)
        <div class="bg-red-100 p-4">
          {{ $errorMessage }}
        </div>
      @endif
    </x-slot>

    <x-slot name="footer">
      <x-secondary-button wire:click="closeModal" wire:loading.attr="disabled">
        Annuleren
      </x-secondary-button>

      <x-button class="ml-2" wire:click.prevent="addOrderLine" wire:loading.attr="disabled">
        Opslaan
      </x-button>
    </x-slot>
  </x-dialog-modal>
</div>
