<div>
  <div class="flex justify-between">
    <div class="w-1/3 flex gap-4">
      <div class="mb-2">
        <x-label for="fromDate" value="Geldig van" />
        <x-input id="fromDate" class="block mt-1 w-full" type="date" wire:model.live="from" />
      </div>
      <div class="mb-2">
        <x-label for="toDate" value="Geldig tot" />
        <x-input id="toDate" class="block mt-1 w-full" type="date" wire:model.live="to" />
      </div>
    </div>
  </div>
  <div class="w-full mt-4">
    <x-button wire:click="$set('showModal', true)">Nieuwe staffel</x-button>
    <table class="table-fixed">
      <thead class="border-b font-bold">
      <tr>
        <th scope="col" class="py-6" width="130">Aantal pallets</th>
        <th scope="col" class="py-6" width="130">Prijs NL</th>
        <th scope="col" class="py-6" width="130">Prijs BE</th>
        <th scope="col" class="py-6" width="130">Prijs ZV</th>
        <th scope="col" class="py-6" width="130">Prijs WE</th>
        <th scope="col" class="py-6" width="60"></th>
      </tr>
      </thead>
      <tbody>
      @foreach($priceList->transportPriceTiers as $index => $priceTier)
        <tr class="border-b @if($index % 2 === 0) bg-gray-100 @endif">
          <td>{{ $priceTier->trailer_spot }}</td>
          <td>&euro;{{ $priceTier->price_nl }}</td>
          <td>&euro;{{ $priceTier->price_be }}</td>
          <td>&euro;{{ $priceTier->price_zv }}</td>
          <td>&euro;{{ $priceTier->price_we }}</td>
          <td><i wire:click="openEditModal({{$priceTier->id}})" class="fa-regular fa-pen-to-square mr-3 cursor-pointer"></i><i wire:click="deleteTier({{ $priceTier->id }})" class="fa-solid fa-trash-can cursor-pointer"></i></td>
        </tr>
      @endforeach
      </tbody>
    </table>
  </div>

  <x-dialog-modal wire:model.live="showModal">
    <x-slot name="title">
      Transport staffel toevoegen
    </x-slot>

    <x-slot name="content">
      <div class="mb-4">
        <x-label for="trailer_spot" value="Aantal pallets" />
        <x-input id="trailer_spot" class="block mt-1 w-full" type="text" wire:model.live="trailerSpot" />
      </div>
      <div class="mb-4">
        <x-label for="price_nl" value="Prijs NL" />
        <x-input id="price_nl" class="block mt-1 w-full" type="text" wire:model.live="priceNL" />
      </div>
      <div class="mb-4">
        <x-label for="price_be" value="Prijs BE" />
        <x-input id="price_be" class="block mt-1 w-full" type="text" wire:model.live="priceBE" />
      </div>
      <div class="mb-4">
        <x-label for="price_zv" value="Prijs ZV" />
        <x-input id="price_zv" class="block mt-1 w-full" type="text" wire:model.live="priceZV" />
      </div>
      <div class="mb-4">
        <x-label for="price_we" value="Prijs WE" />
        <x-input id="price_we" class="block mt-1 w-full" type="text" wire:model.live="priceWE" />
      </div>
    </x-slot>

    <x-slot name="footer">
      <x-secondary-button wire:click="$set('showModal', false)" wire:loading.attr="disabled">
        Annuleren
      </x-secondary-button>

      <x-button class="ml-2" wire:click.prevent="createTier" wire:loading.attr="disabled">
        Opslaan
      </x-button>
    </x-slot>
  </x-dialog-modal>

  <x-dialog-modal wire:model.live="showEditModal">
    <x-slot name="title">
      Transport staffel toevoegen
    </x-slot>

    <x-slot name="content">
      <div class="mb-4">
        <x-label for="trailer_spot" value="Aantal pallets" />
        <x-input id="trailer_spot" class="block mt-1 w-full" type="text" wire:model.live="trailerSpot" />
      </div>
      <div class="mb-4">
        <x-label for="price_nl" value="Prijs NL" />
        <x-input id="price_nl" class="block mt-1 w-full" type="text" wire:model.live="priceNL" />
      </div>
      <div class="mb-4">
        <x-label for="price_be" value="Prijs BE" />
        <x-input id="price_be" class="block mt-1 w-full" type="text" wire:model.live="priceBE" />
      </div>
      <div class="mb-4">
        <x-label for="price_zv" value="Prijs ZV" />
        <x-input id="price_zv" class="block mt-1 w-full" type="text" wire:model.live="priceZV" />
      </div>
      <div class="mb-4">
        <x-label for="price_we" value="Prijs WE" />
        <x-input id="price_we" class="block mt-1 w-full" type="text" wire:model.live="priceWE" />
      </div>
    </x-slot>

    <x-slot name="footer">
      <x-secondary-button wire:click="$set('showModal', false)" wire:loading.attr="disabled">
        Annuleren
      </x-secondary-button>

      <x-button class="ml-2" wire:click.prevent="editTier" wire:loading.attr="disabled">
        Opslaan
      </x-button>
    </x-slot>
  </x-dialog-modal>
</div>
