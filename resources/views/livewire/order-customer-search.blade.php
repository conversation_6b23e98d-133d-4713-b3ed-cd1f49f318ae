<div>
  <input
          type="text"
          wire:model.live.debounce.500ms="search"
          placeholder="<PERSON><PERSON> klant..."
          class="@if(!empty($selectedCustomer)) hidden @endif"
  />
  @unless($customers->isEmpty())
    <div class="bg-gray-100" style="max-height: 400px; overflow-x: scroll; border: 1px solid #4b5563">
      @foreach($customers as $customer)
        <p
                wire:click="selectCustomer({{ $customer->id }})"
                class="cursor-pointer hover:bg-gray-50 py-2 px-2"
                style="margin-bottom: 0px !important;"
        >
          {{ $customer->name }}
        </p>
      @endforeach
    </div>
  @endunless

  @if(!empty($selectedCustomer))
  <div id="billing">
      <strong>Factuuradres</strong><br>
      {{ $selectedCustomer->exact_id }}<br>
      {{ $selectedCustomer->name }}<br>
      {{ $selectedCustomer->address }}<br>
      {{ $selectedCustomer->postal_code }} {{ $selectedCustomer->city }}<br>
      {{ $selectedCustomer->email }}
  </div>
  @endif
</div>
