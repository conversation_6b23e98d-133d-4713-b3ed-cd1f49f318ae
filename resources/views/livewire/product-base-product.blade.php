<div>
  <x-button wire:click="$set('showModal', true)">Toevoegen</x-button>
    <div class="w-3/4">
      <table class="table">
        <thead>
        <tr>
          <th>Naam</th>
{{--          <th>Prijs</th>--}}
          <th>Aantal</th>
          <th>Per</th>
          <th>Winst/verlies product</th>
          <th>Voorraad product</th>
          <th></th>
        </tr>
        </thead>
        <tbody>
          @foreach ($product->baseProducts as $baseProduct)
            <tr>
              <td>{{ $baseProduct->name }}</td>
{{--              <td>&euro; {{ number_format($baseProduct->price, 2) }}</td>--}}
              <td>{{ $baseProduct->pivot->quantity }}</td>
              <td>{{ $baseProduct->pivot->per_qty }}</td>
              <td>{{ $baseProduct->pivot->revenue ? "Ja" : "Nee" }}</td>
              <td>{{ $baseProduct->pivot->stock ? "Ja" : "Nee" }}</td>
              <td><i class="fa-solid fa-trash-can mr-3 cursor-pointer" wire:click="removeBaseProduct({{ $baseProduct->id }})"></i></td>
            </tr>
          @endforeach
        </tbody>
      </table>
  </div>
  <div class="bg-indigo-50 border-l-4 border-orange-500 text-orange-700 p-2 w-1/2" role="alert">
    <p class="font-bold">Help</p>
    <p>
      Hier wordt de kostenopbouw van een product gedefinieerd. De opbouw van de kosten gaat met Basisproducten.<br>
      Een Basisproduct is ook verantwoordelijk voor de voorraad. Door "Voorraad product" op "Ja" te zetten geef je dus aan dat dat Basisproduct het voorraadproduct is voor dit product.<br><br>
      Voorbeeld:<br>
      We hebben 80m3 Franse Boomschors. Vul dus als aantal 80 in en inkoopprijs de prijs per m3.
    </p>
  </div>


  <x-dialog-modal wire:model.live="showModal">
    <x-slot name="title">
      Basis product toevoegen
    </x-slot>

    <x-slot name="content">
      <div class="mb-4">
        <x-label for="product" value="Product" />
        <div class="@if(!empty($selectedBaseProduct)) hidden @endif">
          <input
                  type="text"
                  wire:model.live.debounce.500ms="search"
                  placeholder="Zoek basis product..."
          />
          @unless($baseProducts->isEmpty())
            <div class="bg-gray-100" style="max-height: 400px; overflow-x: scroll; border: 1px solid #4b5563">
              @foreach($baseProducts as $product)
                <p
                        wire:click="selectBaseProduct({{ $product->id }})"
                        class="cursor-pointer hover:bg-gray-50 py-2 px-2"
                        style="margin-bottom: 0px !important;"
                >
                  {{ $product->name }}
                </p>
              @endforeach
            </div>
          @endunless
        </div>
        @if(!empty($selectedBaseProduct))
          {{ $selectedBaseProduct->name }}
        @endif
      </div>
      <div class="mb-4">`
        <x-label for="qty" value="Aantal" />
        <x-input id="qty" class="block mt-1 w-full" type="text" wire:model.live="qty" />
      </div>
      <div class="mb-4">
        <x-label for="per_qty" value="Per X stuks" />
        <x-input id="per_qty" class="block mt-1 w-full" type="text" wire:model.live="perQty" />
      </div>
      <div class="mb-4">
        <label class="toggle">
          <input class="toggle-checkbox" type="checkbox" wire:model.live="revenueProduct">
          <div class="toggle-switch"></div>
          <span class="toggle-label"><strong>Winst/verlies product</strong></span>
        </label>
      </div>
      <div class="mb-4">
        <label class="toggle">
          <input class="toggle-checkbox" type="checkbox" wire:model.live="stockProduct">
          <div class="toggle-switch"></div>
          <span class="toggle-label"><strong>Voorraad product</strong></span>
        </label>
      </div>

    </x-slot>

    <x-slot name="footer">
      <x-secondary-button wire:click="closeModal" wire:loading.attr="disabled">
        Annuleren
      </x-secondary-button>

      <x-button class="ml-2" wire:click.prevent="attachBaseProduct" wire:loading.attr="disabled">
        Opslaan
      </x-button>
    </x-slot>
  </x-dialog-modal>
</div>
