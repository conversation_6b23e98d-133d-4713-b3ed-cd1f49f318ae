<div>
  <table style="width: 100%">
    <thead>
    <tr  style="border-bottom: 2px solid #000; height: 45px;">
      <th style="width: 5%">Nr</th>
      <th style="width: 20%">SKU</th>
      <th style="width: 40%">Product</th>
      <th style="width: 10%">Aantal</th>
      <th style="width: 10%"><PERSON><PERSON><PERSON>rij<PERSON></th>
      <th style="width: 10%">Prijs</th>
      <th style="width: 10%"></th>
    </tr>
    </thead>
    <tbody>
    @foreach($order->orderitems as $index => $orderItem)
      <tr style="border-bottom: 1px solid #ccc; height: 45px;">
        <td>{{ $index + 1 }}</td>
        <td>{{ $orderItem['sku'] }}</td>
        <td>{{ $orderItem['product_name'] }}</td>
        <td>{{ $orderItem['qty'] }}</td>
        <td>&euro; {{ $orderItem['cost'] }}</td>
        <td>&euro; {{ $orderItem['price_ex_vat'] }}</td>
        <td>
          <i class="fa-solid fa-pen-to-square" wire:click="creditOrderline({{$orderItem->id}})"></i>
        </td>
      </tr>
      @if(array_key_exists($orderItem->id, $creditOrderlines))
        <tr style="border-bottom: 2px solid #ccc; height: 45px;">
          <td><i class="fa-sharp fa-solid fa-arrow-turn-up" style="transform: rotate(90deg); margin-left: 10px;"></i></td>
          <td>Credit</td>
          <td></td>
          <td>{{ $orderItem['qty'] -  $creditOrderlines[$orderItem->id]['qty']}}</td>
          <td></td>
          <td>&euro; {{ $orderItem['price_ex_vat'] - $creditOrderlines[$orderItem->id]['price'] }}</td>
          <td><small><strong>-/-</strong></small></td>
        </tr>
        <tr style="border-bottom: 1px solid #ccc; height: 45px;">
          <td></td>
          <td><strong>Total</strong></td>
          <td></td>
          <td><strong>{{ $creditOrderlines[$orderItem->id]['qty']}}</strong></td>
          <td></td>
          <td><strong>&euro; {{ $creditOrderlines[$orderItem->id]['price'] }}</strong></td>
          <td></td>
        </tr>
      @endif
    @endforeach
    </tbody>
  </table>
  <div class="flex justify-content-end" style="border-top: 2px solid #000;">
    <div class="flex-column pr-4 pt-4" style="border-right: 2px solid #000">
      <h5>Kostprijs</h5>
      <div style="width: 400px; margin-left: auto" class="flex justify-between">
        <span>Subtotaal:</span><span>&euro; {{ $order->cost }}</span>
      </div>
      <div style="width: 400px; margin-left: auto" class="flex justify-between">
        <span>Transport:</span><span> &euro; {{ $order->transport_cost }}</span>
      </div>
      <div style="width: 400px; margin-left: auto" class="flex justify-between">
        <span>Totaal:</span><span>&euro; {{ $order->total_cost }}</span>
      </div>
    </div>
    <div class="flex-column ml-4 pt-4">
      <h5>Klantprijs</h5>
      <div style="width: 400px; margin-left: auto" class="flex justify-between">
        <span>Subtotaal:</span><span>&euro; {{ $order->subtotal }}</span>
      </div>
      <div style="width: 400px; margin-left: auto;" >
        <div class="flex justify-between">
          <span>Transport:</span> <span>&euro; {{ $order->transport_price }}</span>
        </div>

      </div>
      <div style="width: 400px; margin-left: auto" class="flex justify-between">
        <span>Totaal:</span><span>&euro; {{ $order->subtotal + $order->transport_price }}</span>
      </div>

    </div>
  </div>
  <x-section-border />
  <div class="w-1-3">
    <x-label>Reden voor creditering</x-label>
    <textarea rows="4" wire:model.live="reason" class="peer h-full min-h-[100px] w-full resize-none rounded-[7px] border border-blue-gray-200 border-t-transparent bg-transparent px-3 py-2.5 font-sans text-sm font-normal text-blue-gray-700 outline outline-0 transition-all placeholder-shown:border placeholder-shown:border-blue-gray-200 placeholder-shown:border-t-blue-gray-200 focus:border-2 focus:border-pink-500 focus:border-t-transparent focus:outline-0 disabled:resize-none disabled:border-0 disabled:bg-blue-gray-50 mb-4"></textarea>
  </div>
  <div>
    <x-button wire:click="saveCredit">Credit Opslaan</x-button>
  </div>

  <x-dialog-modal wire:model.live="showEditOrderlineModal">
    <x-slot name="title">
      Orderregel credit
    </x-slot>
    <x-slot name="content">
      @if($selectedOrderline)
      <strong>{{ $selectedOrderline->product_name }}</strong>
        <div class="mb-4">
          <x-label for="qty" value="Aantal" />
          <x-input id="qty" class="block mt-1 w-full" type="text" wire:model.live="qty" />
        </div>
        <div class="mb-4">
          <x-label for="price" value="Prijs" />
          <x-input id="price" class="block mt-1 w-full" type="text" wire:model.live="price" />
        </div>
      @endif
    </x-slot>
    <x-slot name="footer">
      <x-secondary-button wire:click="cancelCreditOrderline">Annuleren</x-secondary-button>
      <x-button wire:click="saveCreditOrderline">Opslaan</x-button>
    </x-slot>
  </x-dialog-modal>

</div>
