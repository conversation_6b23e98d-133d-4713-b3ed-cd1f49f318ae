<div>
  <div class="mb-4">
    <x-label for="ftpAddress" value="Adres" />
    <x-input id="ftpAddress" class="block mt-1 w-full" type="text" wire:model.live="ftpAddress" autofocus />
  </div>
  <div class="mb-4">
    <x-label for="ftpUsername" value="Gebruikersnaam" />
    <x-input id="ftpUsername" class="block mt-1 w-full" type="text" wire:model.live="ftpUsername" autofocus />
  </div>
  <div class="mb-4">
    <x-label for="ftpPassword" value="Wacthwoord" />
    <x-input id="ftpPassword" class="block mt-1 w-full" type="password" wire:model.live="ftpPassword" />
  </div>
  <x-button wire:click.prevent="saveCredentials" wire:loading.attr="disabled">
    Opslaan
  </x-button>
</div>
