<div>
  <h5>Notities</h5>
  <div class="flex-column flex align-items-start">
  <x-button wire:click="addComment" class="mb-4">Notitie toevoegen</x-button>

  @if($showForm)
    <textarea rows="4" wire:model.live="comment" class="peer h-full min-h-[100px] w-full resize-none rounded-[7px] border border-blue-gray-200 border-t-transparent bg-transparent px-3 py-2.5 font-sans text-sm font-normal text-blue-gray-700 outline outline-0 transition-all placeholder-shown:border placeholder-shown:border-blue-gray-200 placeholder-shown:border-t-blue-gray-200 focus:border-2 focus:border-pink-500 focus:border-t-transparent focus:outline-0 disabled:resize-none disabled:border-0 disabled:bg-blue-gray-50 mb-4"></textarea>
    <x-button wire:click="saveComment" class="mb-4">Opslaan</x-button>
  @endif
  </div>

  <div>
    @foreach($comments as $comment)
      <div class="flex bg-indigo-50 rounded-2 p-2 mb-3">
        <div class="mr-3" style="border-right: 1px solid #ccc; padding-right: 8px; width: 100px;">
          <small>{{ $comment->created_at->format('d-m-Y H:i:s') }}</small><br/>
          <strong>{{ $comment->user->name }}</strong>
        </div>
        <div>
            {{ $comment->comment }}
        </div>
      </div>
    @endforeach
  </div>
</div>
