<div>
  <table id="quotes-table" class="table">
    <thead>
    <tr>
      <th>ID</th>
      <th><PERSON><PERSON></th>
      <th><PERSON><PERSON><PERSON><PERSON><PERSON></th>
      <th>Prijs</th>
    </tr>
    </thead>
  </table>

  @push('scripts')
    <script src="https://code.jquery.com/jquery-3.5.1.js"></script>
    <script src="https://cdn.datatables.net/1.10.23/js/jquery.dataTables.min.js"></script>
    <script>
      document.addEventListener('livewire:init', function () {
        $('#quotes-table').DataTable({
          processing: true,
          serverSide: true,
          ajax: '{{ route('livewire.quotes-table.get-quotes') }}',
          columns: [
            { data: 'id', name: 'id' },
            { data: 'customer.name', name: 'customer'},
            { data: 'user.name', name: 'user' },
            { data: 'total', name: 'total' },
          ],
          rowCallback: function(row, data) {
            $(row).on('click', function() {
              // Redirect to the edit page using the 'id' of the clicked row
              window.location.href = '/quote/edit/' + data.id;
            });
          }
        });
      });
    </script>
  @endpush
</div>
