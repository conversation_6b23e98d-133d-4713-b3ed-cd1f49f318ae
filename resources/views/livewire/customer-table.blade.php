<div>
  <table id="customers-table" class="table">
    <thead>
    <tr>
      <th>ID</th>
      <th>Exact ID</th>
      <th>Naam</th>
      <th>Adres</th>
      <th>Postcode</th>
      <th>Plaats</th>
      <th>Email</th>
    </tr>
    </thead>
  </table>

  @push('scripts')
    <script src="https://code.jquery.com/jquery-3.5.1.js"></script>
    <script src="https://cdn.datatables.net/1.10.23/js/jquery.dataTables.min.js"></script>
    <script>
      document.addEventListener('livewire:init', function () {
        $('#customers-table').DataTable({
          processing: true,
          serverSide: true,
          stateSave: true,
          ajax: '{{ route('livewire.customer-table.get-customers') }}',
          columns: [
            { data: 'id', name: 'id' },
            { data: 'exact_id', name: 'exact_id'},
            { data: 'name', name: 'name' },
            { data: 'address', name: 'address' },
            { data: 'postal_code', name: 'postal_code' },
            { data: 'city', name: 'city' },
            { data: 'email', name: 'email' },
          ],
          rowCallback: function(row, data) {
            $(row).on('click', function() {
              // Redirect to the edit page using the 'id' of the clicked row
              window.location.href = '/customer/edit/' + data.id;
            });
          }
        });
      });
    </script>
  @endpush
</div>
