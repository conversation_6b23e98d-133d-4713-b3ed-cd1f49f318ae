<div class="w-full">
  <h5>Credit</h5>
  <strong>Reden voor credit:</strong> {{ $creditOrder->credit_reason }}<br />
  <strong>Aangemaakt door:</strong> {{ $creditOrder->user->name }} <strong>op</strong> {{ $creditOrder->created_at }}<br/>
  <div class="w-full">
    <table class="table w-full mt-3 mb-0" style="border-bottom: 2px solid #000;">
      <thead>
        <tr>
          <th>Product</th>
          <th>Bestelde aantal</th>
          <th>Credit aantal</th>
          <th>Credit bedrag</th>
        </tr>
      </thead>
      @foreach ($creditOrder->creditLines as $creditOrderline)
        <tr>
          <td>{{ $creditOrderline->orderline->product_name }}</td>
          <td>{{ $creditOrderline->orderline->qty }}</td>
          <td>{{ $creditOrderline->qty }}</td>
          <td>&euro; {{ $creditOrderline->credit_amount }}</td>
        </tr>
      @endforeach
    </table>
    <div class="flex justify-content-end">
      <div style="width: 415px; margin-left: auto ;border-left: 2px solid #000; padding-left: 15px; padding-top: 15px;">
        <h5>Totals</h5>
        <div class="flex justify-between">
          <span>Origineel Totaal</span><span>&euro; {{ $creditOrder->order->total_ex_vat }}</span>
        </div>
        <div class="flex justify-between">
          <span>Credit bedrag</span><span>&euro; {{ $creditOrder->credit_amount }}</span>
        </div>
        <div class="flex justify-between">
          <span>Nieuw totaal order</span><span>&euro; {{ $creditOrder->order->total_ex_vat - $creditOrder->credit_amount }}</span>
        </div>
      </div>
    </div>
  </div>
</div>
