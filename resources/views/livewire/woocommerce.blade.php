<div>
    <h4>WooCommerce SKU's</h4>
    <div class="flex gap-3 w-2/3">
      <x-input id="sku" class="block mt-1 w-1-4" type="text" wire:model.live="sku" placeholder="SKU" autofocus />
      <x-input id="qty" class="block mt-1 w-1-4" type="text" wire:model.live="qty" placeholder="Aantal" />
      <x-button wire:click="addSku">Toevoegen</x-button>
    </div>
    <div>
      <div class="w-1/2">
        <table class="table">
          <thead>
          <tr>
            <th>SKU</th>
            <th>Aantal</th>
            <th></th>
          </tr>
          </thead>
          <tbody>
          @foreach ($product->skus as $sku)
            <tr>
              <td>{{ $sku->sku }}</td>
              <td>{{ $sku->qty }}</td>
              <td><i class="fa-solid fa-trash-can mr-3 cursor-pointer" wire:click="removeSku({{ $sku->id }})"></i></td>
            </tr>
          @endforeach
          </tbody>
        </table>


      @foreach($product->skus as $sku)

      @endforeach
    </div>
</div>
