<div>
    <div class="mb-4">
        <x-secondary-button wire:click="openProductModal">Nieuwe productregel</x-secondary-button>
        <x-secondary-button>Nieuwe vrije regel</x-secondary-button>
    </div>
    <div id="quote-lines" class="mb-4">
        <table class="table-fixed w-full">
            <tr class="border-bottom">
                <th class="p-2">Product</th>
                <th class="p-2">Omschrijving</th>
                <th class="p-2">Aantal</th>
                <th class="p-2">Prijs per eenheid</th>
                <th class="p-2">Prijs</th>
            </tr>
            @foreach ($quoteLines as $quoteLine)
                <tr class="border-bottom align-top">
                    <td class="p-2">{{ $quoteLine['product_name'] }}</td>
                    <td class="p-2">{!! $quoteLine['product_desc'] !!}</td>
                    <td class="p-2">{{ $quoteLine['qty'] }} {{ $quoteLine['qty_type'] }}</td>
                    <td class="p-2">&euro; {{ $quoteLine['price_per_unit'] }}</td>
                    <td class="p-2">&euro; {{ $quoteLine['price'] }}</td>
                </tr>
            @endforeach
        </table>
    </div>
    <div style="margin-top: 50px;">
        <div id="free-text">
            {!! $freeText !!}
        </div>
    </div>
    <div class="w-full flex-row flex justify-content-end">
        <x-button class="align-self-end" wire:click="saveQuote">Opslaan</x-button>
    </div>
    <x-dialog-modal wire:model.live="showProductModal">
    <x-slot name="title">Productregel toevoegen</x-slot>
        <x-slot name="content">
            <div class="mb-4">
                <x-label for="product" value="Product" />
                <div class="@if(!empty($selectedProduct)) hidden @endif">
                    <input
                            type="text"
                            wire:model.live.debounce.500ms="search"
                            placeholder="Zoek product..."
                    />
                    @unless($products->isEmpty())
                        <div class="bg-gray-100" style="max-height: 400px; overflow-x: scroll; border: 1px solid #4b5563">
                            @foreach($products as $product)
                                <p
                                        wire:click="selectProduct({{ $product->id }})"
                                        class="cursor-pointer hover:bg-gray-50 py-2 px-2"
                                        style="margin-bottom: 0px !important;"
                                >
                                    {{ $product->sku }} - {{ $product->name }}
                                </p>
                            @endforeach
                        </div>
                    @endunless
                </div>
            </div>
            @if(!empty($selectedProduct))
                <div class="mb-4">
                    {{ $selectedProduct->name }}
                </div>
                <div class="mb-4">
                    <x-label for="productName" value="Product naam" />
                    <x-input id="productName" class="block mt-1 w-full" type="text" wire:model.live="productName" />
                </div>
                <div class="mb-4">
                    <x-label for="qty" value="Aantal" />
                    <div class="flex">
                        <x-input id="qty" class="block mt-1 w-20" type="text" wire:model.live="qty" />
                        <x-input id="qty_type" class="block mt-1 w-20 ml-4" type="text" wire:model.live="qtyType" />
                    </div>
                </div>
                <div class="mb-4">
                    <x-label for="price_per_unit" value="Prijs per eenheid" />
                    <x-input id="price_per_unit" class="block mt-1 w-20" type="text" wire:model.live="pricePerUnit" />
                </div>
                <div class="mb-4">
                    <x-label for="productDescription" value="Product omschrijving" />
                    <div wire:ignore>
                        <textarea class="min-h-fit h-48 "
                                  name="message"
                                  id="unique_id"></textarea>
                    </div>
                </div>
            @endif
        </x-slot>
        <x-slot name="footer">
            <x-button wire:click="addProductLine">Toevoegen</x-button>
        </x-slot>
    </x-dialog-modal>
</div>
