<div>
  <table id="orders-table" class="table"></table>

  @push('scripts')
    <script src="https://code.jquery.com/jquery-3.5.1.js"></script>
    <script src="https://cdn.datatables.net/1.10.23/js/jquery.dataTables.min.js"></script>
    <script>
      document.addEventListener('livewire:init', function () {
        var table = $('#orders-table').DataTable({
          processing: true,
          serverSide: true,
          stateSave: true,
          ajax: '{{ route('livewire.order-table.get-orders') }}',
          columns: [
            { data: 'id', name: 'id', width: '40px' },
            {
              data: 'order_date',
              name: 'order_date',
              width: '100px',
              render: function (data, type, row) {
                if (type === 'display' || type === 'filter') {
                  var date = new Date(data);
                  var day = ("0" + date.getDate()).slice(-2);
                  var month = ("0" + (date.getMonth() + 1)).slice(-2);
                  var year = date.getFullYear();

                  return day + '-' + month + '-' + year;
                }
                return data;
              }
            },
            { data: 'status', name: 'status', width: '100px'},
            { data: 'customer.name', name: 'customer.name' },
            {
              data: 'address.name',
              name: 'address.name',
              render: function (data, type, row) {
                if (row.address && row.address.company_name && row.address.company_name.length > 0) {
                  return row.address.company_name + ' - ' + row.address.city;
                } else if (row.address && row.address.name) {
                  return row.address.name + ' - ' + row.address.city;
                } else {
                  return 'AFHALEN';
                }
              }
            },
            {
              data: 'hasShipment',
              name: 'hasShipment',
              width: '50px',
              className: 'text-center',
              render: function (data, type, row) {
                return data ? '<i class="fa-solid fa-circle-check" style="color: #41d24b;"></i>' : '<i class="fa-solid fa-circle-xmark" style="color: #ea3939;"></i>';
              }
            },
            {
              data: 'reference',
              name: 'reference',
              width: '80px',
            },
            {
              data: 'delivery_date',
              name: 'delivery_date',
              width: '100px',
              render: function (data, type, row) {
                var date = new Date(data);

                var day = ("0" + date.getDate()).slice(-2);
                var month = ("0" + (date.getMonth() + 1)).slice(-2); // getMonth() is zero-based
                var year = date.getFullYear();

                return day + '-' + month + '-' + year;
              }
            },
            {
              data: 'total_ex_vat',
              name: 'total_ex_vat',
              width: '120px',
              render: function (data, type, row) {
                var revenueColor = parseFloat(row.revenue) < 0 ? 'red' : 'green';
                return '€ ' + parseFloat(data).toFixed(2) + '<br><span style="color: ' + revenueColor + ';">(€ ' + parseFloat(row.revenue).toFixed(2) + ')</span>';
              }
            },
          ],
          columnDefs: [
            { title: "ID", targets: 0 },
            { title: "Besteldatum", targets: 1 },
            { title: "Status", targets: 2 },
            { title: "Klant", targets: 3 },
            { title: "Eindklant", targets: 4 },
            { title: "TM", targets: 5 },
            { title: "Referentie", targets: 6 },
            { title: "Leverdatum", targets: 7 },
            { title: "Prijs/Omzet", targets: 8 },
          ],
          order: [[0, 'desc']],
          rowCallback: function(row, data) {
            $(row).on('click', function() {
              // Redirect to the edit page using the 'id' of the clicked row
              window.location.href = '/order/edit/' + data.id;
            });
            if (data.status === 'CANCELLED') {
              $(row).find('td').css('color', 'red');
            }
          }
        });

        $('#statusFilter').on('change', function() {
          var selectedStatus = $(this).val();
          table.columns(2).search(selectedStatus).draw(); // Assuming status is in the third column
        });

        table.columns().every(function(index) {
          var colheader = $(table.column(index).header());

          if (index === 5) {
              colheader.attr('title', 'Is deze bestelling al aangemeld bij Transmission.');
          }
        });

      });
    </script>
  @endpush
</div>
