<div>
    <hr>
  <div class="w-1/2 flex justify-between">
    <h4>Voorraad</h4>
    <x-button wire:click="$set('showModal', true)"><i class="fa-solid fa-plus" style="color: #ffffff; margin-right: 4px;"></i> Voorra<PERSON> toevoegen</x-button>
  </div>
    <div class="w-1/2">
      <table class="table">
        <thead>
        <tr>
          <th>Aantal</th>
          <th>Inkoop</th>
          <th>Transport</th>
          <th></th>
        </tr>
        </thead>
        <tbody>
        @foreach($baseProduct->stock as $stockPlace)
          <tr>
            <td>{{ $stockPlace->quantity }}</td>
            <td>{{ $stockPlace->price }}</td>
            <td>{{ $stockPlace->transport_cost }}</td>
            <td>
              <i class="fa-regular fa-pen-to-square mr-3 cursor-pointer" wire:click="editStockPlace({{ $stockPlace->id }})"></i>
              <i class="fa-solid fa-trash-can mr-3 cursor-pointer" wire:click="removeStockPlace({{ $stockPlace->id }})"></i>
            </td>
          </tr>
        @endforeach
        </tbody>
      </table>
    </div>
  <div class="bg-indigo-50 border-l-4 border-orange-500 text-orange-700 p-2 w-1/2" role="alert">
    <p class="font-bold">Help</p>
    <p>
      Hier kan de voorraad van dit Basisproduct worden bijgehouden. Elk product wat dit Basisproduct als "voorraad product" heeft haalt hier de voorraad vandaan en de inkoopprijs.<br><br>
      Wanneer een Basisproduct voorraad heeft dan wordt er gerekend met de inkoopprijs die bij de voorraad staat en niet met de prijs die bij het Basisproduct zelf is ingevuld.
    </p>
  </div>
  <x-dialog-modal wire:model.live="showModal">
    <x-slot name="title">
      Voorraad toevoegen
    </x-slot>
    <x-slot name="content">
      <div class="mb-4">
        <x-label for="qty" value="Aantal" />
        <x-input id="qty" class="block mt-1 w-full" type="text" wire:model.live="qty" autofocus />
        @error('qty') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
      </div>
      <div class="mb-4">
        <x-label for="cost" value="Inkoop prijs per eenheid" />
        <x-input id="cost" class="block mt-1 w-full" type="text" wire:model.live="cost" autofocus />
        @error('cost') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
      </div>
      <div class="mb-4">
        <x-label for="cost" value="Transport prijs totaal" />
        <x-input id="cost" class="block mt-1 w-full" type="text" wire:model.live="transportcost" autofocus /><br>
        <small>Transport kosten worden opgeslagen door dit veld te delen door het aantal.</small>
        @error('cost') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
      </div>

    </x-slot>

    <x-slot name="footer">
      <x-secondary-button wire:click="cancelModal" wire:loading.attr="disabled">
        Annuleren
      </x-secondary-button>

      <x-button class="ml-2" wire:click.prevent="saveStockPlace" wire:loading.attr="disabled">
        Opslaan
      </x-button>
    </x-slot>
  </x-dialog-modal>
</div>
