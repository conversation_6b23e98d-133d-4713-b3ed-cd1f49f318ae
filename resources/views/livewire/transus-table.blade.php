<div>
  <table id="transus-table" class="table">
    <thead>
    <tr>
      <th>ID</th>
      <th>Transus ID</th>
      <th>Bestelnummer</th>
      <th>Verwerkt</th>
      <th>Datum/tijd</th>
    </tr>
    </thead>
  </table>

  @push('scripts')
    <script src="https://code.jquery.com/jquery-3.5.1.js"></script>
    <script src="https://cdn.datatables.net/1.10.23/js/jquery.dataTables.min.js"></script>
    <script>
      document.addEventListener('livewire:init', function () {
        var table = $('#transus-table').DataTable({
          processing: true,
          stateSave: true,
          serverSide: true,
          ajax: '{{ route('livewire.transus-table.get-imports') }}',
          columns: [
            { data: 'id', name: 'id' },
            { data: 'transus_id', name: 'transus_id'},
            { data: 'order_id', name: 'order_id'},
            {
              data: 'parsed',
              name: 'parsed',
              width: '60px',
              className: 'text-center',
              render: function (data, type, row) {
                return data ? '<i class="fa-solid fa-circle-check" style="color: #41d24b;"></i>' : '<i class="fa-solid fa-circle-xmark" style="color: #ea3939;"></i>';
              }
            },
            {
              data: 'created_at',
              name: 'created_at',
              render: function (data, type, row) {
                if (type === 'display' || type === 'filter') {
                  var date = new Date(data);
                  var day = ("0" + date.getDate()).slice(-2);
                  var month = ("0" + (date.getMonth() + 1)).slice(-2);
                  var year = date.getFullYear();
                  var hours = ("0" + date.getHours()).slice(-2);
                  var minutes = ("0" + date.getMinutes()).slice(-2);

                  return day + '-' + month + '-' + year + ' ' + hours + ':' + minutes;
                }
                return data;
              }
            },
          ],
          order: [[0, 'desc']],
          rowCallback: function(row, data) {
            $(row).on('click', function() {
              // Redirect to the edit page using the 'id' of the clicked row
              window.location.href = '/transus/view/' + data.id;
            });
          }
        });

      });
    </script>
  @endpush
</div>
