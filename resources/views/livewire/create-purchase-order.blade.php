<div>
  <div class="flex" style="height: 170px; gap: 13rem">
    <div>
      <input
              type="text"
              wire:model.live.debounce.500ms="customerSearch"
              placeholder="<PERSON><PERSON> leverancier..."
              class="@if(!empty($selectedCustomer)) hidden @endif"
      />
      @unless($customers->isEmpty())
        <div class="bg-gray-100" style="max-height: 400px; overflow-x: scroll; border: 1px solid #4b5563">
          @foreach($customers as $customer)
            <p
                    wire:click="selectCustomer({{ $customer->id }})"
                    class="cursor-pointer hover:bg-gray-50 py-2 px-2"
                    style="margin-bottom: 0px !important;"
            >
              {{ $customer->name }}
            </p>
          @endforeach
        </div>
      @endunless

      @if(!empty($selectedCustomer))
        <div id="billing">
          <strong>Leverancier</strong><br>
          {{ $selectedCustomer->exact_id }}<br>
          {{ $selectedCustomer->name }}<br>
          {{ $selectedCustomer->address }}<br>
          {{ $selectedCustomer->postal_code }} {{ $selectedCustomer->city }}<br>
          {{ $selectedCustomer->email }}
        </div>
      @endif
    </div>
    <div>
      <div class="mb-4">
        <strong>Laaddatum</strong>
        <x-input id="laadDatum" class="block mt-1 w-full" type="date" wire:model.live="loadDate" />
      </div>
      <div class="mb-4">
        <strong>Leverdatum</strong>
        <x-input id="aantal" class="block mt-1 w-full" type="date" wire:model.live="deliveryDate" />
      </div>
    </div>
  </div>
  <div id="orderlines">
    @if(!empty($selectedCustomer))
      <x-button wire:click="$set('showModal', true)">Regel toevoegen</x-button>
    @endif
      @if(!empty($orderLines))
        <table style="width: 100%">
          <thead>
          <tr  style="border-bottom: 2px solid #000; height: 45px;">
            <th style="width: 5%">Nr</th>
            <th style="width: 20%">SKU</th>
            <th style="width: 40%">Product</th>
            <th style="width: 10%">Aantal</th>
            <th style="width: 10%">Prijs p/e</th>
            <th style="width: 10%">Prijs</th>
            <th style="width: 10%"></th>
          </tr>
          </thead>
          <tbody>

          @foreach($orderLines as $index => $orderItem)
            <tr style="border-bottom: 1px solid #ccc; height: 45px;">
              <td>{{ $index + 1 }}</td>
              <td>{{ $orderItem['product']['sku'] }}</td>
              <td>{{ $orderItem['product']['name'] }}</td>
              <td>{{ $orderItem['qty'] }}</td>
              <td>€{{ $orderItem['unit_price'] }}</td>
              <td>€{{ $orderItem['unit_price'] * $orderItem['qty'] }}</td>
              <td><i class="fa-solid fa-trash" wire:click="removeOrderline({{$index}})" style="color: #dd2222;"></i></td>
            </tr>
{{--            @if($orderItem['storage_product'])--}}
{{--              <tr style="border-bottom: 1px solid #ccc; height: 45px;" class="text-sm">--}}
{{--                <td><i class="fa-sharp fa-solid fa-arrow-turn-up" style="transform: rotate(90deg); margin-left: 10px;"></i></td>--}}
{{--                <td>{{ $orderItem['storage_product']['sku'] }}</td>--}}
{{--                <td>{{ $orderItem['storage_product']['name'] }}</td>--}}
{{--                <td>{{ $orderItem['qty'] * $orderItem['product']['storage_product_qty'] }}</td>--}}
{{--                <td></td>--}}
{{--                <td></td>--}}
{{--                <td></td>--}}
{{--              </tr>--}}
{{--            @endif--}}
          @endforeach

          </tbody>
        </table>
      @endif
  </div>
  <div id="totals">
    <div class="flex justify-content-end mt-6">
      <x-button wire:click="saveOrder">Bestelling opslaan</x-button>
    </div>
  </div>

  <x-dialog-modal wire:model.live="showModal">
    <x-slot name="title">
      Regel toevoegen
    </x-slot>

    <x-slot name="content">
      <div class="mb-4">
        <x-label for="product" value="Product" />
        <div class="@if(!empty($selectedProduct)) hidden @endif">
          <input
                  type="text"
                  wire:model.live.debounce.500ms="searchProduct"
                  placeholder="Zoek product..."
          />
          @unless($products->isEmpty())
            <div class="bg-gray-100" style="max-height: 400px; overflow-x: scroll; border: 1px solid #4b5563">
              @foreach($products as $product)
                <p
                        wire:click="selectProduct({{ $product->id }})"
                        class="cursor-pointer hover:bg-gray-50 py-2 px-2"
                        style="margin-bottom: 0px !important;"
                >
                  {{ $product->sku }} - {{ $product->name }}
                </p>
              @endforeach
            </div>
          @endunless
        </div>
        @if(!empty($selectedProduct))
          {{ $selectedProduct->name }}
        @endif
      </div>
      <div class="mb-4">
        <x-label for="aantal" value="Aantal" />
        <x-input id="aantal" class="block mt-1 w-full" type="number" wire:model.live="qty" />
      </div>
      <div class="mb-4">
        <x-label for="price" value="Prijs per eenheid" />
        <x-input id="price" class="block mt-1 w-full" type="number" wire:model.live="purchasePrice" />
      </div>
      @if (!empty($stockPlaces))
        <div class="mb-4">
          <x-label for="stockPlace" value="Voorraadplaats" />
          <select id="stockPlace" wire:model.live="selectedStockPlace" class="mt-1 block w-full">
            <option value="new">Nieuwe voorraadplaats</option>
            @foreach($stockPlaces as $stockPlace)
              <option value="{{$stockPlace['id']}}">{{ $stockPlace['name'] }} - €{{$stockPlace['cost']}}</option>
            @endforeach
          </select>
        </div>
      @endif
    </x-slot>

    <x-slot name="footer">
      <x-secondary-button wire:click="$set('showModal', false)" wire:loading.attr="disabled">
        Annuleren
      </x-secondary-button>

      <x-button class="ml-2" wire:click.prevent="addOrderLine" wire:loading.attr="disabled">
        Opslaan
      </x-button>
    </x-slot>
  </x-dialog-modal>
</div>
