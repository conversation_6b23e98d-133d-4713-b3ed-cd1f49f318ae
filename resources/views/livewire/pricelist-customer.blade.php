<div>
  @if($errorMessage)
    <div class="alert alert-danger">{{ $errorMessage }}</div>
  @endif
  <input
          type="text"
          wire:model.live.debounce.500ms="search"
          placeholder="<PERSON><PERSON> klant..."
  />
  @unless($customers->isEmpty())
    <div class="bg-gray-100" style="max-height: 400px; overflow-x: scroll; border: 1px solid #4b5563">
      @foreach($customers as $customer)
        <p
                wire:click="selectCustomer({{ $customer->id }})"
                class="cursor-pointer hover:bg-gray-50 py-2 px-2"
                style="margin-bottom: 0px !important;"
        >
          {{$customer->exact_id}} - {{ $customer->name }}
        </p>
      @endforeach
    </div>
  @endunless
  <div class="pt-4">
    <h4>Klanten</h4>
    @unless($attachedCustomers->isEmpty())
      <div>
        <table class="table">
          <thead>
            <tr>
              <th>Exact ID</th>
              <th>Naam</th>
              <th>Adres</th>
              <th>Postcode</th>
              <th>Plaats</th>
              <th></th>
            </tr>
          </thead>
          <tbody>
            @foreach($attachedCustomers as $attachedCustomer)
              <tr>
                <td>{{ $attachedCustomer->exact_id }}</td>
                <td>{{ $attachedCustomer->name }}</td>
                <td>{{ $attachedCustomer->address }}</td>
                <td>{{ $attachedCustomer->postal_code }}</td>
                <td>{{ $attachedCustomer->city }}</td>
                <td><i class="fa-regular fa-trash-can cursor-pointer" style="color: #db2814;" wire:click="removeCustomer({{$attachedCustomer->id}})"></i></td>
              </tr>
            @endforeach
          </tbody>
        </table>

      </div>
    @endunless
  </div>
</div>
