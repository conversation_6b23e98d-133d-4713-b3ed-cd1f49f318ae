<div>
  @if(!empty($selectedCustomer) && !empty($postalCode))
  <x-button wire:click="$set('showModal', true)">Regel toevoegen</x-button>
  @endif
  <x-dialog-modal wire:model.live="showModal">
    <x-slot name="title">
      Regel toevoegen
    </x-slot>

    <x-slot name="content">
      <div class="mb-4">
        <x-label for="product" value="Product" />
        <div class="@if(!empty($selectedProduct)) hidden @endif">
          <input
                  type="text"
                  wire:model.live.debounce.500ms="search"
                  placeholder="Zoek product..."
          />
          @unless($products->isEmpty())
            <div class="bg-gray-100" style="max-height: 400px; overflow-x: scroll; border: 1px solid #4b5563">
              @foreach($products as $product)
                <p
                        wire:click="selectProduct({{ $product->id }})"
                        class="cursor-pointer hover:bg-gray-50 py-2 px-2"
                        style="margin-bottom: 0px !important;"
                >
                 {{ $product->sku }} - {{ $product->name }}
                </p>
              @endforeach
            </div>
          @endunless
        </div>
        @if(!empty($selectedProduct))
          {{ $selectedProduct->name }}
        @endif
      </div>
      <div class="mb-4">
        <x-label for="aantal" value="Aantal" />
        <x-input id="aantal" class="block mt-1 w-full" type="number" wire:model.live="quantity" />
        @if(!empty($selectedProduct))
          @if ($selectedProduct->transport_qty)
            <small><strong>LET OP! Aantal per transport eenheid is {{$selectedProduct->transport_qty}}. Aantal moet dus deelbaar zijn door {{$selectedProduct->transport_qty}}</strong></small>
          @endif
        @endif
      </div>
      @if($errorMessage)
        <div class="bg-red-100 p-4">
          {{ $errorMessage }}
        </div>
      @endif
    </x-slot>

    <x-slot name="footer">
      <x-secondary-button wire:click="$set('showModal', false)" wire:loading.attr="disabled">
        Annuleren
      </x-secondary-button>

      <x-button class="ml-2" wire:click.prevent="addOrderLine" wire:loading.attr="disabled">
        Opslaan
      </x-button>
    </x-slot>
  </x-dialog-modal>
    @if(!empty($orderLines))
      <table style="width: 100%">
        <thead>
        <tr  style="border-bottom: 2px solid #000; height: 45px;">
          <th style="width: 5%">Nr</th>
          <th style="width: 20%">SKU</th>
          <th style="width: 40%">Product</th>
          <th style="width: 10%">Aantal</th>
          <th style="width: 10%">Kostprijs</th>
          <th style="width: 10%">Prijs</th>
          <th style="width: 10%"></th>
        </tr>
        </thead>
        <tbody>

            @foreach($orderLines as $index => $orderItem)
                <tr style="border-bottom: 1px solid #ccc; height: 45px;">
                    <td>{{ $index + 1 }}</td>
                    <td>{{ $orderItem['product']['sku'] }}</td>
                    <td>{{ $orderItem['product']['name'] }}<br><small>{{ $orderItem['price_calculation'] }}</small></td>
                    <td>{{ $orderItem['qty'] }}</td>
                    <td></td>
                    <td>&euro; {{ $orderItem['price'] }}</td>
                    <td><i class="fa-solid fa-trash" wire:click="removeOrderline({{$index}})" style="color: #dd2222;"></i></td>
                </tr>
              @if($orderItem['storage_product'])
                <tr style="border-bottom: 1px solid #ccc; height: 45px;" class="text-sm">
                  <td><i class="fa-sharp fa-solid fa-arrow-turn-up" style="transform: rotate(90deg); margin-left: 10px;"></i></td>
                  <td>{{ $orderItem['storage_product']['sku'] }}</td>
                  <td>{{ $orderItem['storage_product']['name'] }}</td>
                  <td>{{ $orderItem['qty'] * $orderItem['product']['storage_product_qty'] }}</td>
                  <td></td>
                  <td></td>
                  <td></td>
                </tr>
              @endif
            @endforeach

        </tbody>
      </table>
      @if(!$shipperSelected)
        <div class="mt-2 mb-2 flex justify-end">
          <x-button wire:click="addTransmissionTransport">Transmission verzendkosten</x-button>
{{--          <x-button wire:click="$set('showTransportModal', true)">Verzenden via planning</x-button>--}}
        </div>
      @endif
    @endif

    <x-dialog-modal wire:model.live="showTransportModal">
      <x-slot name="title">
        Transportkosten toevoegen
      </x-slot>
      <x-slot name="content">
        <div class="mb-4">
          <x-label for="cost_price" value="Kostprijs" />
          <x-input id="cost_price" class="block mt-1 w-full" type="text" wire:model.live="transportCostPrice" />
        </div>
        <div class="mb-4">
          <x-label for="price" value="Prijs" />
          <x-input id="price" class="block mt-1 w-full" type="text" wire:model.live="transportPrice" />
        </div>
      </x-slot>
      <x-slot name="footer">
        <x-secondary-button wire:click="$set('showTransportModal', false)" wire:loading.attr="disabled">
          Annuleren
        </x-secondary-button>

        <x-button class="ml-2" wire:click.prevent="addTransportPrice" wire:loading.attr="disabled">
          Opslaan
        </x-button>
      </x-slot>
    </x-dialog-modal>
</div>
