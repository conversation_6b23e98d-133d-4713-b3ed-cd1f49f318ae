@php use Carbon\Carbon; @endphp
<div>
  <div class="flex" style="height: 170px; gap: 13rem">
    <div id="billing">
      <strong>Leverancier</strong><br>
      {{ $purchaseOrder->customer->exact_id }}<br>
      {{ $purchaseOrder->customer->name }}<br>
      {{ $purchaseOrder->customer->address }}<br>
      {{ $purchaseOrder->customer->postal_code }} {{ $purchaseOrder->customer->city }}<br>
      {{ $purchaseOrder->customer->email }}
    </div>
    <div id="delivery">
      <strong>Leverdatum</strong><br>
      {{ $purchaseOrder->delivery_date }}
    </div>
    <div>
      @if($purchaseOrder->status === 'RECEIVED')
        <strong>Levering geregistreerd: {{ Carbon::create($purchaseOrder->updated_at)->format('d-m-Y H:m') }}</strong>
        <br>
        @if($purchaseOrder->transport_cost == 0.00)
          <x-button wire:click="$set('showTransportModal', true)">Transportkosten toevoegen</x-button>
        @else
          <strong>Transport kosten: €{{$purchaseOrder->transport_cost}}</strong><br>
          <x-button wire:click="addToStock">Voorraad verwerken</x-button>
        @endif
      @endif
      @if($purchaseOrder->status === 'COMPLETED')
        <strong>Voorraad verwerkt op {{ Carbon::create($purchaseOrder->updated_at)->format('d-m-Y H:m') }}</strong><br>
        <strong>Transport kosten: €{{$purchaseOrder->transport_cost}}</strong>
      @endif
    </div>
  </div>

  <div id="orderlines">
    <table style="width: 100%">
      <thead>
      <tr style="border-bottom: 2px solid #000; height: 45px;">
        <th style="width: 5%">Nr</th>
        <th style="width: 20%">SKU</th>
        <th style="width: 35%">Product</th>
        <th style="width: 10%">Aantal besteld</th>
        <th style="width: 10%">Aantal ontv.</th>
        <th style="width: 10%">Prijs p/e</th>
        <th style="width: 10%">Prijs</th>
        <th style="width: 10%"></th>
      </tr>
      </thead>
      <tbody>

      @foreach($purchaseOrder->purchaseOrderlines as $index => $orderItem)
        <tr style="border-bottom: 1px solid #ccc; height: 45px;">
          <td>{{ $index + 1 }}</td>
          <td>{{ $orderItem->product->sku }}</td>
          <td>{{ $orderItem->product->name }}</td>
          <td>{{ $orderItem->qty }}</td>
          <td>@if($orderItem->qty_received){{ $orderItem->qty_received }}@endif</td>
          <td>€{{ $orderItem->purchase_price }}</td>
          <td>€{{ $orderItem->purchase_price * $orderItem->qty }}</td>
          @if($orderItem->qty_received)
            <td><i class="fa-regular fa-circle-check" style="color: #0fdb27;"></i></td>
          @else
            <td><i class="fa-solid fa-truck-ramp-box cursor-pointer"
                   wire:click="registerDelivery({{$orderItem->id}})"></i></td>
          @endif
        </tr>
        @if($orderItem->transport_cost > 0.00)
          <tr style="border-bottom: 2px solid #ccc; height: 45px;">
            <td><i class="fa-sharp fa-solid fa-arrow-turn-up" style="transform: rotate(90deg); margin-left: 10px;"></i></td>
            <td></td>
            <td>&nbsp;&nbsp;&nbsp;&nbsp;Transport kosten per eenheid</td>
            <td></td>
            <td></td>
            <td>€{{ number_format($orderItem->transport_cost, 2) }}</td>
            <td></td>
            <td></td>
          </tr>
        @endif
      @endforeach

      </tbody>
    </table>
  </div>
  @if(!$sending)
    <x-button wire:click="sendToPlanbord" class="mt-3">Verstuur naar planbord</x-button>
  @else
    <x-button class="mt-3">Bezig met versturen</x-button>
  @endif
  <x-dialog-modal wire:model.live="showReceivedModal">
    <x-slot name="title">
      Ontvangst registreren
    </x-slot>
    <x-slot name="content">
      @if($selectedOrderline)
        <div class="mb-4">
          <x-label for="aantal" value="Aantal ontvangen"/>
          <x-input id="aantal" class="block mt-1 w-full" type="number" wire:model.live="qty"/>
        </div>
      @endif
    </x-slot>
    <x-slot name="footer">
      <x-button wire:click="received">Opslaan</x-button>
    </x-slot>
  </x-dialog-modal>

  <x-dialog-modal wire:model.live="showTransportModal">
    <x-slot name="title">
      Transportkosten toevoegen
    </x-slot>
    <x-slot name="content">
        <div class="mb-4">
          <x-label for="aantal" value="Transport kosten"/>
          <x-input id="aantal" class="block mt-1 w-full" type="number" wire:model.live="transportCost"/>
        </div>
    </x-slot>
    <x-slot name="footer">
      <x-button wire:click="saveTransportCost">Opslaan</x-button>
    </x-slot>
  </x-dialog-modal>
</div>
