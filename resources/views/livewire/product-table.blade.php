<div>
  <table id="products-table" class="table">
    <thead>
    <tr>
      <th>ID</th>
      <th>SKU</th>
      <th>Naam</th>
      <th>Omschrijving</th>
      <th>GTIN</th>
      <th>Pricelists</th>
      <th>Voorraad</th>
      <th>Basis producten</th>
    </tr>
    </thead>
  </table>

  @push('scripts')
    <script src="https://code.jquery.com/jquery-3.5.1.js"></script>
    <script src="https://cdn.datatables.net/1.10.23/js/jquery.dataTables.min.js"></script>
    <script>
      document.addEventListener('livewire:init', function () {
        $('#products-table').DataTable({
          processing: true,
          serverSide: true,
          stateSave: true,
          ajax: '{{ route('livewire.product-table.get-products') }}',
          columns: [
            { data: 'id', name: 'id' },
            { data: 'sku', name: 'sku'},
            { data: 'name', name: 'name' },
            { data: 'description', name: 'description' },
            { data: 'gtin', name: 'gtin' },
            { data: 'pricelists', name: 'pricelists' },
            { data: 'has_base_stock', name: 'has_base_stock' },
            { data: 'base_products', name: 'base_products' },
          ],
          rowCallback: function(row, data) {
            $(row).on('click', function() {
              window.location.href = '/product/edit/' + data.id;
            });
          }
        });
      });
    </script>
  @endpush
</div>
