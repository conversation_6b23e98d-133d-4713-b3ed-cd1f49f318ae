<div>
    <div class="flex items-center mb-6">
      <div class="w-1/3">
        <label for="type" class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4">Type product</label>
      </div>
      <div class="w-2/3">
        <select id="type" class="form-input rounded-md shadow-sm mt-1 block w-full" wire:model.live="type">
          <option value="retail">Retail</option>
          <option value="bustotaal">BusTotaal</option>
        </select>
      </div>
    </div>
    <div class="flex items-center mb-6">
      <div class="w-1/3">
        <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4" for="name" value="{{ __('Naam') }}" />
      </div>
      <div class="w-2/3">
        <x-input id="name" class="block mt-1 w-full" type="text" name="name" wire:model.live="name" />
      </div>
    </div>
    <div class="flex items-center mb-6">
      <div class="w-1/3">
        <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4" for="sku" value="{{ __('SKU') }}" />
      </div>
      <div class="w-2/3">
        <x-input id="sku" class="block mt-1 w-full" type="text" name="sku" wire:model.live="sku" />
      </div>
    </div>
    <div class="flex items-start mb-6">
      <div class="w-1/3">
        <x-label class="block text-gray-500 font-bold md:text-right mt-2 mb-1 md:mb-0 pr-4" for="description" value="{{ __('Omschrijving') }}" />
      </div>
      <div class="w-2/3">
        <x-textarea id="description" class="block mt-1 w-full" type="text" name="description" wire:model.live="description" />
      </div>
    </div>
    <div class="flex items-center mb-6">
      <div class="w-1/3">
        <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4" for="cost" value="{{ __('Kostprijs') }}" />
      </div>
      <div class="w-2/3">
        <x-input id="cost" class="block mt-1 w-full" type="text" name="cost" wire:model.live="cost" />
      </div>
    </div>
    <div class="flex items-center mb-6">
      <div class="w-1/3">
        <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4" for="price" value="{{ __('Prijs') }}" />
      </div>
      <div class="w-2/3">
        <x-input id="price" class="block mt-1 w-full" type="text" name="price" wire:model.live="price"/>
      </div>
    </div>
    @if ($type === 'retail')
    <div class="flex items-center mb-6">
      <div class="w-1/3">
        <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4" for="gtin" value="GTIN"/>
      </div>
      <div class="w-2/3">
        <x-input id="gtin" class="block mt-1 w-full" type="text" name="gtin" wire:model.live="gtin" />
      </div>
    </div>
    @endif
    <div class="flex items-center mb-6">
      <div class="w-1/3">
        <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4" for="transport_qty" value="{{ __('Transport Eenheid Aantal') }}" />
      </div>
      <div class="w-2/3">
        <x-input id="transport_qty" class="block mt-1 w-full" type="text" name="transport_qty" wire:model.live="transportQty"  />
        <small><i>Hoeveel gaan er op een pallet?</i></small>
      </div>
    </div>
    <div class="flex items-center mb-6">
      <div class="w-1/3">
      </div>
      <div class="w-2/3">
        <x-button wire:click="saveProduct">
          {{ __('Opslaan') }}
        </x-button>
      </div>
    </div>
</div>
