<div>
    <div class="select-pricelist">
        <select id="priceAgreementSelect" wire:model="priceAgreementId" class="form-control">
            <option value="">-- Choose an option --</option>
            @foreach($priceAgreements as $id => $name)
                <option value="{{ $id }}">{{ $name }}</option>
            @endforeach
        </select>
        <button wire:click="load">Laad prijslijst</button>
    </div>
    <div class="product" style="margin-top: 40px;">
        <div class="product-top" style="padding-left: 5px;">
            <strong>Transportprijzen</strong> (indien van toepassing)
        </div>
        <div class="product-tiers">
            <table class="transporttable" style="text-align: left" cellspacing="0" cellpadding="0">
                <tr>
                    <th width="20%" style="padding-left: 5px;">Aantal pallets</th>
                    <th width="20%">Prijs Ned<PERSON></th>
                    <th width="20%">Prijs Belgie</th>
                    <th width="20%">Prijs Zweeuws Vlaanderen</th>
                    <th width="20%">Prijs Waddeneilanden</th>
                </tr>
                <tbody>
                @foreach($priceAgreement->transportPriceTiers as $transportTier)
                    <tr>
                        <td style="padding-left: 5px;">{{ $transportTier->trailer_spot }}</td>
                        <td>&euro; {{ number_format($transportTier->price_nl,2 ) }}</td>
                        <td>&euro; {{ number_format($transportTier->price_be, 2) }}</td>
                        <td>&euro; {{ number_format($transportTier->price_zv, 2) }}</td>
                        <td>&euro; {{ number_format($transportTier->price_we, 2) }}</td>
                    </tr>
                @endforeach
                </tbody>
            </table>
        </div>
    </div>
</div>
