<div>
    <x-button wire:click="$set('showModal', true)">Nieuw basis product</x-button>
    <table id="base-products-table" class="table"></table>

    @push('scripts')
        <script src="https://code.jquery.com/jquery-3.5.1.js"></script>
        <script src="https://cdn.datatables.net/1.10.23/js/jquery.dataTables.min.js"></script>
        <script>
            document.addEventListener('livewire:init', function () {
                var table = $('#base-products-table').DataTable({
                    processing: true,
                    serverSide: true,
                    stateSave: true,
                    ajax: '{{ route('livewire.base-product-table.get-products') }}',
                    columns: [
                        { data: 'id', name: 'id', width: '40px' },
                        { data: 'name', name: 'name'},
                        {
                            data: 'price',
                            name: 'price',
                            render: function (data, type, row) {

                                return '€ ' + parseFloat(data).toFixed(2);
                            }
                        },
                    ],
                    columnDefs: [
                        { title: "ID", targets: 0 },
                        { title: "Naam", targets: 1 },
                        { title: "Price", targets: 2 },
                    ],
                    order: [[0, 'asc']],
                    rowCallback: function(row, data) {
                        $(row).on('click', function() {
                            // Redirect to the edit page using the 'id' of the clicked row
                            window.location.href = '/basisproducten/edit/' + data.id;
                        });
                    }
                });
            });
        </script>
    @endpush

    <x-dialog-modal wire:model.live="showModal">
        <x-slot name="title">
            Basis product toevoegen
        </x-slot>

        <x-slot name="content">
            <div class="mb-4">
                <x-label for="name" value="Naam" />
                <x-input id="name" class="block mt-1 w-full" type="text" wire:model.live="name" />
            </div>
            <div class="mb-4">
                <x-label for="price" value="Prijs" />
                <x-input id="price" class="block mt-1 w-full" type="text" wire:model.live="price" />
            </div>

        </x-slot>

        <x-slot name="footer">
            <x-secondary-button wire:click="closeModal" wire:loading.attr="disabled">
                Annuleren
            </x-secondary-button>

            <x-button class="ml-2" wire:click.prevent="createBaseProduct" wire:loading.attr="disabled">
                Opslaan
            </x-button>
        </x-slot>
    </x-dialog-modal>

</div>
