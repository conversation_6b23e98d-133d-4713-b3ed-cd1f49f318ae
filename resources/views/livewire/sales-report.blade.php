<div class="p-6 lg:p-8 bg-white border-b border-gray-200">
  <style>
    @media print {
      body * {
        visibility: hidden;
      }

      button, button * {
        visibility: hidden !important;
      }

      #printreport {
        visibility: hidden;
      }

      #salesreport, #salesreport * {
        visibility: visible;
      }

      #salesreport {
        position: absolute;
        left: 0;
        top: 0;
      }
    }
  </style>
  <div id="salesreport"
       x-init="document.addEventListener('printsales', event => {
            window.print();
        });">
  <div class="flex align-items-baseline mb-4">
    <x-input id="from" class="block mt-1 w-48 mr-3" type="date" wire:model.live="fromDate" />
    <strong>tot</strong>
    <x-input id="to" class="block mt-1 w-48 ml-3 mr-3" type="date" wire:model.live="toDate" />
    <x-button id="showreport" class="ml-2" wire:click.prevent="showRapport" wire:loading.attr="disabled">
      Toon
    </x-button>
    <x-button id="printreport" wire:click="print">Print Rapport</x-button>
    <x-button id="exportReport" wire:click="exportReport">Export Rapport</x-button>
    <x-button id="exportHornbach" wire:click="exportHornbach">Export Hornbach</x-button>
  </div>

  <div class="w-full mt-2 flex gap-4 flex-wrap">
      <div class="rounded bg-gray-200 p-4 relative" style="width: 19rem;">
        <h5><i class="fa-solid fa-euro-sign"></i> Omzet</h5>
        <hr>
        <div class="flex justify-between">
          <span>Netto Omzet: </span>
          <span>&euro; {{ number_format($totalSales, 2, ',', '') }}</span>
        </div>
        <div class="flex justify-between">
          <span>Kosten: </span>
          <span>&euro; {{ number_format($totalCost, 2, ',', '') }}</span>
        </div>
        <div class="flex justify-between">
          <span>Welkoop formule bijdrage 8% over &euro; {{ number_format($totalSalesWelkoop, 2, ',', '') }}: </span>
          <span>&euro; {{ number_format(($totalSalesWelkoop / 100) * 8, 2, ',', '') }}</span>
        </div>
        <hr>
        <div class="flex justify-between">
          <span>Marge: </span>
          <span>&euro; {{ number_format(($totalSales - ($totalSalesWelkoop / 100) * 8) - $totalCost, 2, '.', '') }}</span>
        </div>
        <div class="flex justify-between">
          <span>Marge %: </span>
          <span>@if($totalSales > 0){{ number_format(((($totalSales - ($totalSalesWelkoop / 100) * 8) - $totalCost) / ($totalSales - ($totalSalesWelkoop / 100) * 8)) * 100, 2, '.', '') }}@endif%</span>
        </div>
      </div>
      <div class="rounded bg-gray-200 p-4 relative" style="width: 19rem;">
        <h5><i class="fa-solid fa-truck"></i> Transport</h5>
        <hr>
        <div class="flex justify-between">
          <span>Pallets: </span>
          <span>{{ $pallets }}</span>
        </div>
        <div class="flex justify-between">
          <span>Hornbach Pallets: </span>
          <span>{{ $hornbachPallets }}</span>
        </div>
        <div class="flex justify-between">
          <span>Vrachten: </span>
          <span>{{ number_format($pallets / 24, 2, '.', '') }}</span>
        </div>
      </div>
  </div>
  <div class="w-full mt-4 flex gap-4 flex-wrap">
      <div class="rounded bg-gray-200 p-4 relative" style="width: 19rem;">
        <h5><i class="fa-solid fa-user-tie"></i> Klanten (top 10)</h5>
        <hr>
        @foreach($customers as $index => $customer)
          <div>
            @if($customer['name'] == 'Welkoop Retail B.V')
              @php
                $total = ($customer['total_sales'] / 100) * 92;
              @endphp
              <strong>{{ $customer['name'] }}</strong>
              <div class="flex justify-between">
                <span>Bruto Omzet: </span>
                <span>&euro; {{ number_format($customer['total_sales'], 2, ',', '') }}</span>
              </div>
              <div class="flex justify-between">
                <span>8% Formule bijdrage: </span>
                <span>&euro; {{ number_format(($customer['total_sales'] / 100) * 8, 2, ',', '') }}</span>
              </div>
              <div class="flex justify-between">
                <span>Netto Omzet: </span>
                <span>&euro; {{ number_format($total, 2, ',', '') }}</span>
              </div>
              <div class="flex justify-between">
                <span>Marge: </span>
                <span>&euro; {{ number_format($total - $customer['total_cost'], 2, ',', '') }}</span>
              </div>
              <div class="flex justify-between">
                <span>Marge %: </span>
                <span>{{ number_format((($total - $customer['total_cost']) / $total) * 100, 2, ',', '') }}%</span>
              </div>
            @else
              <strong>{{ $customer['name'] }}</strong>
              <div class="flex justify-between">
                <span>Netto Omzet: </span>
                <span>&euro; {{ number_format($customer['total_sales'], 2, ',', '') }}</span>
              </div>
              <div class="flex justify-between">
                <span>Marge: </span>
                <span>&euro; {{ number_format($customer['total_sales'] - $customer['total_cost'], 2, ',', '') }}</span>
              </div>
              <div class="flex justify-between">
                <span>Marge %: </span>
                <span>{{ number_format((($customer['total_sales'] - $customer['total_cost']) / $customer['total_sales']) * 100, 2, ',', '') }}%</span>
              </div>
            @endif
          </div>
          <hr>
        @endforeach
      </div>
      <div class="rounded bg-gray-200 p-4 relative" style="width: 19rem;">
        <h5><i class="fa-solid fa-box-open"></i> Producten (top 10)</h5>
        <hr>
        @foreach($products as $product)
          <div>
            <strong>{{ $product['sku'] }} - {{ $product['name'] }}</strong>
            <div class="flex justify-between">
              <span>Pallets: </span>
              <span>{{ $product['pallets'] }}</span>
            </div>
          </div>
          <hr>
        @endforeach
      </div>
  </div>
  </div>
</div>
