<div>
  <div class="flex justify-between">
    <div class="w-1/3 flex gap-4">
      <div class="mb-2">
        <x-label for="fromDate" value="Geldig van" />
        <x-input id="fromDate" class="block mt-1 w-full" type="date" wire:model.live="from" />
      </div>
      <div class="mb-2">
        <x-label for="toDate" value="Geldig tot" />
        <x-input id="toDate" class="block mt-1 w-full" type="date" wire:model.live="to" />
      </div>
    </div>
    <div>
      <a class="text-decoration-none mt-2 inline-flex items-center px-4 py-2 bg-white border border-gray-300 rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-25 transition ease-in-out duration-150"
         href="{{ route('pricelist.print', $pricelist->id) }}" target="_blank">PDF Prijslijst</a>
    </div>
  </div>
    <div class="w-full mt-4">
      @foreach($products as $product)
        <div class="rounded w-full bg-gray-200 p-2 mb-4">
          <div class="flex justify-between">
            <div class="w-1/3">
              <div>{{ $product->sku }}</div>
              <div>{{ $product->name }}</div>
              <div>
                @if($product->transport_qty)
                  <small>
                    Er gaan {{ $product->transport_qty }} op een pallet.
                  </small>
                @endif
              </div>
            </div>
            <div class="w-1/3 pl-3" style="border-left: 1px solid #000;">
              <div>
                <h5>Prijsstaffels Dropship</h5>
                <label class="toggle">
                  <input type="checkbox"
                         wire:model.live="dropShipCheckboxValues.{{ $product->id }}"
                         wire:click="toggleDropshipFreeShipping({{ $product->id }})"
                          {{ array_key_exists($product->id ,$dropShipCheckboxValues) && $dropShipCheckboxValues[$product->id] == 1 ? 'checked' : '' }}>
                  <span class="toggle-label">Gratis verzending</span>
                </label>
              </div>
              <table class="table-fixed mb-4">
                <thead class="border-b font-bold">
                <tr>
                  <th scope="col" class="py-1" width="100">Van</th>
                  <th scope="col" class="py-1" width="100">Tot</th>
                  <th scope="col" class="py-1" width="140">Prijs per eenheid</th>
                  <th></th>
                </tr>
                </thead>
                <tbody>
                @if(array_key_exists($product->id, $dropShipProductPrices))
                  @foreach($dropShipProductPrices[$product->id] as $priceTier)
                    <tr style="border-top: 1px solid #000">
                      <td>{{ $priceTier['from_qty'] }}</td>
                      <td>{{ $priceTier['to_qty'] }}</td>
                      <td>&euro; {{ $priceTier['price'] }}</td>
                      <td><i class="fa-regular fa-trash-can cursor-pointer" style="color: #db2814;" wire:click="removeTier({{$product->id}}, {{$priceTier['id']}}, 'D')"></i></td>
                    </tr>
                  @endforeach
                @endif
                </tbody>
              </table>
              <x-button wire:click="addTier({{$product->id}}, 'D')">Prijsstaffel toevoegen</x-button>
            </div>
            <div class="w-1/3 pl-3" style="border-left: 1px solid #000;">
              <div>
                <h5>Prijsstaffels Bevoorrading</h5>
                <label class="toggle">
                  <input type="checkbox"
                         wire:model.live="checkboxValues.{{ $product->id }}"
                         wire:click="toggleFreeShipping({{ $product->id }})"
                          {{ array_key_exists($product->id ,$checkboxValues) && $checkboxValues[$product->id] == 1 ? 'checked' : '' }}>
                  <span class="toggle-label">Gratis verzending</span>
                </label>
              </div>
              <table class="table-fixed mb-4">
                <thead class="border-b font-bold">
                <tr>
                  <th scope="col" class="py-1" width="100">Van</th>
                  <th scope="col" class="py-1" width="100">Tot</th>
                  <th scope="col" class="py-1" width="140">Prijs per eenheid</th>
                  <th></th>
                </tr>
                </thead>
                <tbody>
                @if(array_key_exists($product->id, $productPrices))
                  @foreach($productPrices[$product->id] as $priceTier)
                    <tr style="border-top: 1px solid #000">
                      <td>{{ $priceTier['from_qty'] }}</td>
                      <td>{{ $priceTier['to_qty'] }}</td>
                      <td>&euro; {{ $priceTier['price'] }}</td>
                      <td><i class="fa-regular fa-trash-can cursor-pointer" style="color: #db2814;" wire:click="removeTier({{$product->id}}, {{$priceTier['id']}}, 'S')"></i></td>
                    </tr>
                  @endforeach
                @endif
                </tbody>
              </table>
              <x-button wire:click="addTier({{$product->id}}, 'S')">Prijsstaffel toevoegen</x-button>
            </div>
          </div>
        </div>
      @endforeach
        <x-danger-button wire:click="deletePricelist">Verwijder prijslijst</x-danger-button>
    </div>

  <x-dialog-modal wire:model.live="showModal">
    <x-slot name="title">
      Prijsstaffel toevoegen - {{ $productName }}
    </x-slot>

    <x-slot name="content">
      @if($errorMessage)
        <div class="alert alert-danger">{{ $errorMessage }}</div>
      @endif
      <div class="mb-4">
        <x-label for="fromQty" value="Vanaf" />
        <x-input id="fromQty" class="block mt-1 w-full" type="number" wire:model.live="fromQty" autofocus />
      </div>
      <div class="mb-4">
        <x-label for="toQty" value="Tot" />
        <x-input id="toQty" class="block mt-1 w-full" type="number" wire:model.live="toQty" />
      </div>
      <div class="mb-4">
        <x-label for="newPrice" value="Price" />
        <x-input id="newPrice" class="block mt-1 w-full" type="number" wire:model.live="newPrice" />
      </div>
    </x-slot>

    <x-slot name="footer">
      <x-secondary-button wire:click="$set('showModal', false)" wire:loading.attr="disabled">
        Annuleren
      </x-secondary-button>

      <x-button class="ml-2" wire:click.prevent="saveTier" wire:loading.attr="disabled">
        Opslaan
      </x-button>
    </x-slot>
  </x-dialog-modal>


</div>
