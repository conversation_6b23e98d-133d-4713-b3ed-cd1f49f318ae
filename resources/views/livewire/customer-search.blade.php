<div>
  <h4><PERSON><PERSON> Prijzen</h4>
  <input
          type="text"
          wire:model.live.debounce.500ms="search"
          placeholder="<PERSON><PERSON> klant..."
  />
  @unless($customers->isEmpty())
  <div class="bg-gray-100" style="max-height: 400px; overflow-x: scroll; border: 1px solid #4b5563">
    @foreach($customers as $customer)
      <p
              wire:click="selectCustomer({{ $customer->id }})"
              class="cursor-pointer hover:bg-gray-50 py-2 px-2"
              style="margin-bottom: 0px !important;"
      >
        {{ $customer->name }}
      </p>
    @endforeach
  </div>
  @endunless

  @if(!empty($selectedCustomer))
    <div class="mt-6">
      <h5>Prijsstaffels voor {{ $selectedCustomer->name }}</h5>
      <table class="table-fixed mb-4">
        <thead class="border-b font-bold">
        <tr>
          <th scope="col" class="py-1" width="100">Van</th>
          <th scope="col" class="py-1" width="100">Tot</th>
          <th scope="col" class="py-1" width="100">Prijs</th>
          <th scope="col" class="py-1" width="100">Datum van</th>
          <th scope="col" class="py-1" width="100">Datum tot</th>
          <th></th>
        </tr>
        </thead>
        <tbody>
        @foreach($customerPrices as $index => $customerPrice)
          <tr class="border-b @if($index % 2 === 0) bg-gray-100 @endif">
            <td>{{ $customerPrice->from_qty }}</td>
            <td>{{ $customerPrice->to_qty }}</td>
            <td>&euro;{{ $customerPrice->price }}</td>
            <td>{{ $customerPrice->from?->format('d-m-Y') }}</td>
            <td>{{ $customerPrice->to?->format('d-m-Y') }}</td>
            <td><i class="fa-solid fa-trash" style="color: #e8305e;" wire:click="showDeleteTier({{ $customerPrice->id }})"></i></td>
          </tr>
        @endforeach
        </tbody>
      </table>
      <x-button wire:click="$set('showModal', true)">Prijsstaffel toevoegen</x-button>
    </div>


    <x-dialog-modal wire:model.live="showModal">
      <x-slot name="title">
        Prijsstaffel toevoegen
      </x-slot>

      <x-slot name="content">
        @if($errorMessage)
          <div class="alert alert-danger">{{ $errorMessage }}</div>
        @endif
        <div class="mb-4">
          <x-label for="fromQty" value="Vanaf" />
          <x-input id="fromQty" class="block mt-1 w-full" type="number" wire:model.live="fromQty" autofocus />
        </div>
        <div class="mb-4">
          <x-label for="toQty" value="Tot" />
          <x-input id="toQty" class="block mt-1 w-full" type="number" wire:model.live="toQty" />
        </div>
        <div class="mb-4">
          <x-label for="newPrice" value="Price" />
          <x-input id="newPrice" class="block mt-1 w-full" type="number" wire:model.live="newPrice" />
        </div>
        <div class="mb-4">
          <x-label for="fromDate" value="From Date" />
          <x-input id="fromDate" class="block mt-1 w-full" type="date" wire:model.live="from" />
        </div>
        <div class="mb-4">
          <x-label for="toDate" value="To Date" />
          <x-input id="toDate" class="block mt-1 w-full" type="date" wire:model.live="to" />
        </div>
      </x-slot>

      <x-slot name="footer">
        <x-secondary-button wire:click="$set('showModal', false)" wire:loading.attr="disabled">
          Annuleren
        </x-secondary-button>

        <x-button class="ml-2" wire:click.prevent="addCustomerPrice" wire:loading.attr="disabled">
          Opslaan
        </x-button>
      </x-slot>
    </x-dialog-modal>

    <x-dialog-modal wire:model.live="showModalDelete">
      <x-slot name="title">
        {{ __('Verwijder staffel') }}
      </x-slot>

      <x-slot name="content">
        {{ __('Weet je zeker dat je de staffel wil verwijderen?') }}
      </x-slot>

      <x-slot name="footer">
        <x-secondary-button wire:click="$toggle('showModalDelete')" wire:loading.attr="disabled">
          {{ __('Annuleren') }}
        </x-secondary-button>

        <x-danger-button class="ml-2" wire:click="deleteTier" wire:loading.attr="disabled">
          {{ __('Verwijder Staffel') }}
        </x-danger-button>
      </x-slot>
    </x-dialog-modal>
  @endif

</div>
