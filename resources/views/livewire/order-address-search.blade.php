<div>
  <div class="@if(!empty($selectedAddress)) hidden @endif">
  <div class="@if(empty($selectedCustomer)) hidden @endif">
    <x-button wire:click="pickup">Afhalen</x-button>
    <x-button wire:click="$set('showModal', true)">Nieuw adres</x-button>
    <br>of
  </div>
  <input
          type="text"
          wire:model.live.debounce.500ms="search"
          placeholder="Zoek adres..."
          class="@if(empty($selectedCustomer)) hidden @endif"
  />
  @unless($addresses->isEmpty())
    <div class="bg-gray-100" style="max-height: 400px; overflow-x: scroll; border: 1px solid #4b5563">
      @foreach($addresses as $address)
        <p
                wire:click="selectAddress({{ $address->id }})"
                class="cursor-pointer hover:bg-gray-50 py-2 px-2"
                style="margin-bottom: 0px !important;"
        >
          {{ $address->company_name }} - {{ $address->name }}
        </p>
      @endforeach
    </div>
  @endunless
  </div>

  @if(!empty($selectedAddress))
    <div id="shipping">
      @if($selectedAddress === 'pickup')
        <strong>Afhalen</strong>
      @else
        <strong>Verzendadres</strong><br>
        @if($selectedAddress->company_name){{ $selectedAddress->company_name }}<br>@endif
        {{ $selectedAddress->name }}<br>
        {{ $selectedAddress->street }} {{ $selectedAddress->housenumber }}<br>
        {{ $selectedAddress->postal_code }} {{ $selectedAddress->city }}<br>
        {{ $selectedAddress->country }}<br>
        {{ $selectedAddress->email }}
      @endif
    </div>
  @endif

  <x-dialog-modal wire:model.live="showModal">
    <x-slot name="title">
      Nieuw adres
    </x-slot>

    <x-slot name="content">
      <div class="mb-4">
        <x-label for="companyName" value="Bedrijfsnaam" />
        <x-input id="companyName" class="block mt-1 w-full" type="text" wire:model.live="companyName" autofocus />
      </div>
      <div class="mb-4">
        <x-label for="name" value="Naam" />
        <x-input id="name" class="block mt-1 w-full" type="text" wire:model.live="name" />
      </div>
        <div class="mb-4">
          <x-label for="street" value="Straat" />
          <x-input id="street" class="block mt-1 w-full" type="text" wire:model.live="street" />
        </div>
        <div class="mb-4">
          <x-label for="housenumber" value="Huisnummer" />
          <x-input id="housenumber" class="block mt-1 w-full" type="text" wire:model.live="housenumber" />
        </div>
      <div class="mb-4">
          <x-label for="postalcode" value="Postcode" />
          <x-input id="postalcode" class="block mt-1 w-full" type="text" wire:model.live="postalCode" />
        </div>
      <x-button wire:click="searchAddress">Zoek adres</x-button>
      @if($addressSearchError)
        <div class="alert alert-danger mt-4">Kan geen adres vinden met deze postcode en huisnummer.</div>
      @endif
        <div class="mb-4">
          <x-label for="city" value="Plaats" />
          <x-input id="city" class="block mt-1 w-full" type="text" wire:model.live="city" />
        </div>
      <div class="mb-4">
        <label for="country" class="block font-medium text-sm text-gray-700">Country</label>
        <select id="country" class="form-input rounded-md shadow-sm mt-1 block w-full" wire:model.live="country">
          <option value="">Selecteer Land</option>
          <option value="NL">Nederland</option>
          <option value="BE">Belgie</option>
        </select>
      </div>
      <div class="mb-4">
        <x-label for="email" value="Email" />
        <x-input id="email" class="block mt-1 w-full" type="email" wire:model.live="email" />
      </div>
      <div class="mb-4">
        <x-label for="telephone" value="Telefoon" />
        <x-input id="telephone" class="block mt-1 w-full" type="text" wire:model.live="telephone" />
      </div>
      <div class="mb-4">
        <x-label for="gln" value="GLN" />
        <x-input id="gln" class="block mt-1 w-full" type="text" wire:model.live="gln" />
      </div>
    </x-slot>

    <x-slot name="footer">
      <x-secondary-button wire:click="$set('showModal', false)" wire:loading.attr="disabled">
        Annuleren
      </x-secondary-button>

      <x-button class="ml-2" wire:click.prevent="saveAddress" wire:loading.attr="disabled">
        Opslaan
      </x-button>
    </x-slot>
  </x-dialog-modal>
</div>
