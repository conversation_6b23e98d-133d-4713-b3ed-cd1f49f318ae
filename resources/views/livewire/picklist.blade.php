@php use Carbon\Carbon; @endphp
<div>
  <style>
    @media print {
      body * {
        visibility: hidden;
      }

      #picklist, #picklist * {
        visibility: visible;
      }

      #picklist {
        position: absolute;
        left: 0;
        top: 0;
      }
    }
  </style>
  @if (!empty($pickListItems))
    <div class="flex-row font-bold"
         id="picklist"
         x-init="document.addEventListener('print', event => {
            window.print();
        });">
      <h2>Retail Picklist voor {{ Carbon::createFromFormat('Y-m-d', $pickDate)->format('d-m-Y') }}</h2>

      <h4>Hornbach</h4>
      @foreach($hornbachPicklistItems as $pickItem)
        <div class="text-2xl">{{ $pickItem['total_qty'] }} X {{ $pickItem['name'] }}</div>
      @endforeach
      <hr>
      <h4>Welkoop</h4>
      @foreach($welkoopPicklistItems as $pickItem)
        <div class="text-2xl">{{ $pickItem['total_qty'] }} X {{ $pickItem['name'] }}</div>
      @endforeach
      <hr>
      <h4>Overig</h4>
      @foreach($otherPicklistItems as $pickItem)
        <div class="text-2xl">{{ $pickItem['total_qty'] }} X {{ $pickItem['name'] }}</div>
      @endforeach
    </div>
  @else
    <h2>Geen bestellingen voor {{ Carbon::createFromFormat('Y-m-d', $pickDate)->format('d-m-Y') }}</h2>
  @endif

  @if(!empty($pickedItems))
    <x-section-border />
    <x-section-border />
    <div>
      <div class="flex justify-between">
      <h4>
        Reeds geprint voor {{ Carbon::createFromFormat('Y-m-d', $pickDate)->format('d-m-Y') }}
      </h4>
        <x-button wire:click="reprintLabels">Reprint</x-button>
      </div>
      @foreach($pickedItems as $pickedItem)
        <div class="text-2xl">{{ $pickedItem['total_qty'] }} X {{ $pickedItem['name'] }}</div>
      @endforeach
    </div>
  @endif
</div>
