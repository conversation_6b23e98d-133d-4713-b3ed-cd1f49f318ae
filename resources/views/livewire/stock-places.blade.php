<div>
  <div class="flex justify-between">
    <h4><PERSON><PERSON><PERSON><PERSON> plaatsen</h4>
    @if (!$product->storageProduct)
      <x-button wire:click="$set('showModal', true)"><i class="fa-solid fa-plus" style="color: #ffffff; margin-right: 4px;"></i> Voorraad toevoegen</x-button>
    @endif
  </div>
  @if (!$product->storageProduct)
  <div class="w-1/2">
    <table class="table">
      <thead>
        <tr>
          <th>Naam</th>
          <th>Aantal</th>
          <th>Inkoop</th>
          <th></th>
        </tr>
      </thead>
      <tbody>
        @foreach($product->stockPlaces as $stockPlace)
          <tr>
            <td>{{ $stockPlace->name }}</td>
            <td>{{ $stockPlace->qty }}</td>
            <td>{{ $stockPlace->cost }}</td>
            <td>
              <i class="fa-regular fa-pen-to-square mr-3 cursor-pointer" wire:click="editStockPlace({{ $stockPlace->id }})"></i>
              <i class="fa-solid fa-trash-can mr-3 cursor-pointer" wire:click="removeStockPlace({{ $stockPlace->id }})"></i>
              @if($stockPlace->position > 1)<i class="fa-solid fa-arrow-up cursor-pointer" wire:click="movePositionUp({{ $stockPlace->id }})"></i>@endif
              @if($stockPlace->position < $product->stockPlaces->count() )<i class="fa-solid fa-arrow-down cursor-pointer" wire:click="movePositionDown({{ $stockPlace->id }})"></i>@endif
            </td>
          </tr>
        @endforeach
      </tbody>
    </table>
  </div>
  @else
    <p>
      Dit product kan zelf geen voorraad hebben. De voorraad wordt bijgehouden op het voorraad product.
    </p>
  @endif
  <x-dialog-modal wire:model.live="showModal">
    <x-slot name="title">
      Voorraad toevoegen
    </x-slot>
    <x-slot name="content">
      <div class="mb-4">
        <x-label for="name" value="Naam" />
        <x-input id="name" class="block mt-1 w-full" type="text" wire:model.live="name" autofocus />
        @error('name') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
      </div>
      <div class="mb-4">
        <x-label for="qty" value="Aantal" />
        <x-input id="qty" class="block mt-1 w-full" type="text" wire:model.live="qty" autofocus />
        @error('qty') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
      </div>
      <div class="mb-4">
        <x-label for="cost" value="Inkoop prijs" />
        <x-input id="cost" class="block mt-1 w-full" type="text" wire:model.live="cost" autofocus />
        @error('cost') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
      </div>

    </x-slot>

    <x-slot name="footer">
      <x-secondary-button wire:click="cancelModal" wire:loading.attr="disabled">
        Annuleren
      </x-secondary-button>

      <x-button class="ml-2" wire:click.prevent="saveStockPlace" wire:loading.attr="disabled">
        Opslaan
      </x-button>
    </x-slot>
  </x-dialog-modal>
</div>
