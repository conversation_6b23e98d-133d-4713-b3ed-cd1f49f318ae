<div>
  <div id="billing" class="mb-4">
    {{ $quote->customer->name }}<br>
    T.a.v. {{ $quote->customer_name }}<br>
    {{ $quote->customer->address }}<br>
    {{ $quote->customer->postal_code }} {{ $quote->customer->city }}<br>
    {{ $quote->customer_email }}
  </div>
  <div id="quote-lines" class="mb-4">
    <table class="table-fixed w-full">
      <tr class="border-bottom">
        <th class="p-2">Product</th>
        <th class="p-2">Omschrijving</th>
        <th class="p-2">Aantal</th>
        <th class="p-2">Prijs per eenheid</th>
        <th class="p-2">Prijs</th>
      </tr>
      @foreach ($quote->quoteLines as $quoteLine)
        <tr class="border-bottom align-top">
          <td class="p-2">{{ $quoteLine->name }}</td>
          <td class="p-2">{!! $quoteLine->line_desc !!}</td>
          <td class="p-2">{{ $quoteLine->qty }} {{ $quoteLine->qty_type }}</td>
          <td class="p-2">&euro; {{ $quoteLine->price }}</td>
          <td class="p-2 relative">
            &euro; {{ $quoteLine->price * $quoteLine->qty }}
            <i class="fa-regular fa-pen-to-square absolute right-0 cursor-pointer" wire:click="editQuoteLine({{ $quoteLine->id }})"></i>
          </td>
        </tr>
      @endforeach
    </table>
  </div>
  <div style="margin-top: 50px;">
    <div id="free-text">
      {!! $quote->extra_info !!}
    </div>
  </div>


  <x-dialog-modal wire:model.live="showEditProductModal">
    <x-slot name="title">Productregel aanpassen</x-slot>
    <x-slot name="content">
        <div class="mb-4">
          <x-label value="Product"/>
          @if ($currentProduct)
            {{ $currentProduct->sku }} || {{ $currentProduct->name }}
          @endif
        </div>
        <div class="mb-4">
          <x-label for="productName" value="Product naam" />
          <x-input id="productName" class="block mt-1 w-full" type="text" wire:model.live="productName" />
        </div>
        <div class="mb-4">
          <x-label for="qty" value="Aantal" />
          <div class="flex">
            <x-input id="qty" class="block mt-1 w-20" type="text" wire:model.live="productQty" />
            <x-input id="qty_type" class="block mt-1 w-20 ml-4" type="text" wire:model.live="productQtyType" />
          </div>
        </div>
        <div class="mb-4">
          <x-label for="price_per_unit" value="Prijs per eenheid" />
          <x-input id="price_per_unit" class="block mt-1 w-20" type="text" wire:model.live="price" />
        </div>
        <div class="mb-4">
          <x-label for="productDescription" value="Product omschrijving" />
          <div wire:ignore>
              <textarea class="min-h-fit h-48 "
                        name="message"
                        id="unique_id" wire:model.live="productDesc"></textarea>
          </div>
        </div>
    </x-slot>
    <x-slot name="footer">
      <div class="flex justify-between w-full">
          <div>
            <x-danger-button wire:click="removeQuoteLine">Verijder</x-danger-button>
          </div>
          <div>
            <x-secondary-button wire:click="$set('showEditProductModal', false)">Annuleren</x-secondary-button>
            <x-button wire:click="updateProductLine">Opslaan</x-button>
          </div>
      </div>
    </x-slot>
  </x-dialog-modal>
</div>
