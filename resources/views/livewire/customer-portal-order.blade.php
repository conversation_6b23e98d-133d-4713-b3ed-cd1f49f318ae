<div>
  <div class="flex justify-between">
    <div>
      <strong>Verzendadres</strong><br>
      @if($order->address->company_name) {{ $order->address->company_name }}<br> @endif
      {{ $order->address->name }}<br>
      {{ $order->address->street }} {{ $order->address->housenumber }}<br>
      {{ $order->address->postal_code }} {{ $order->address->city }} {{ $order->address->country }}<br>
      {{ $order->address->telephone }}<br>
      {{ $order->address->email }}<br><br>
    </div>
    <div>
      <strong>Referentie:</strong> {{ $order->reference }}<br>
      <strong>Status: </strong>{{ $order->status }}<br>
      @if ($order->shipment)
        <a class="text-decoration-none mt-2 inline-flex items-center px-4 py-2 bg-white border border-gray-300 rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-25 transition ease-in-out duration-150"
           href="{{ $order->shipment->track_trace }}" target="_blank">Track&Trace</a><br>
      @endif
    </div>
  </div>
  <table style="width: 100%">
    <thead>
    <tr  style="border-bottom: 2px solid #000; height: 45px;">
      <th style="width: 5%">Nr</th>
      <th style="width: 20%">SKU</th>
      <th style="width: 40%">Product</th>
      <th style="width: 10%">Aantal</th>
    </tr>
    </thead>
    <tbody>
    @foreach($order->orderitems as $index => $orderItem)
      <tr  style="border-bottom: 1px solid #ccc; height: 45px;">
        <td>{{ $index + 1 }}</td>
        <td>{{ $orderItem['sku'] }}</td>
        <td>{{ $orderItem['product_name'] }} <br> <span style="{{ str_contains($orderItem['calculation'], 'Geen') ? 'color: red;' : '' }}"></td>
        <td>{{ $orderItem['qty'] }}</td>
      </tr>
    @endforeach
    </tbody>
  </table>
  
</div>
