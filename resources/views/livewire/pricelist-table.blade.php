<div>
  <x-button wire:click="$set('showModal', true)">Prijslijst toevoegen</x-button>
  <table id="pricelist-table" class="table">
    <thead>
    <tr>
      <th>ID</th>
      <th>Naam</th>
      <th><PERSON><PERSON><PERSON> van</th>
      <th>G<PERSON>ig tot</th>
      <th>Klanten</th>
    </tr>
    </thead>
  </table>

  <x-dialog-modal wire:model.live="showModal">
    <x-slot name="title">
      Prijslijst toevoegen
    </x-slot>

    <x-slot name="content">
      <div class="mb-4">
        <x-label for="name" value="Naam prijslijst (bv.: Dealers Q1)" />
        <x-input id="name" class="block mt-1 w-full" type="text" wire:model.live="name" />
      </div>
      <div class="flex gap-4">
        <div class="mb-4">
          <x-label for="fromDate" value="Geldig van" />
          <x-input id="fromDate" class="block mt-1 w-full" type="date" wire:model.live="from" />
        </div>
        <div class="mb-4">
          <x-label for="toDate" value="Geldig tot" />
          <x-input id="toDate" class="block mt-1 w-full" type="date" wire:model.live="to" />
        </div>
      </div>
    </x-slot>

    <x-slot name="footer">
      <x-secondary-button wire:click="$set('showModal', false)" wire:loading.attr="disabled">
        Annuleren
      </x-secondary-button>

      <x-button class="ml-2" wire:click.prevent="createPriceList" wire:loading.attr="disabled">
        Opslaan
      </x-button>
    </x-slot>
  </x-dialog-modal>

  @push('scripts')
    <script src="https://code.jquery.com/jquery-3.5.1.js"></script>
    <script src="https://cdn.datatables.net/1.10.23/js/jquery.dataTables.min.js"></script>
    <script>
      document.addEventListener('livewire:init', function () {
        $('#pricelist-table').DataTable({
          processing: true,
          serverSide: true,
          stateSave: true,
          ajax: '{{ route('livewire.pricelist-table.get-pricelists') }}',
          columns: [
            { data: 'id', name: 'id' },
            { data: 'name', name: 'name' },
            {
              data: 'from',
              name: 'from',
              render: function (data, type, row) {
                if (type === 'display' || type === 'filter') {
                  var date = new Date(data);
                  var day = ("0" + date.getDate()).slice(-2);
                  var month = ("0" + (date.getMonth() + 1)).slice(-2);
                  var year = date.getFullYear();

                  return day + '-' + month + '-' + year;
                }
                return data;
              }
            },
            {
              data: 'to',
              name: 'to',
              render: function (data, type, row) {
                if (type === 'display' || type === 'filter') {
                  var date = new Date(data);
                  var day = ("0" + date.getDate()).slice(-2);
                  var month = ("0" + (date.getMonth() + 1)).slice(-2);
                  var year = date.getFullYear();

                  return day + '-' + month + '-' + year;
                }
                return data;
              }
            },
            {
              data: 'customer_count',
              name: 'customer_count'
            },
          ],
          rowCallback: function(row, data) {
            $(row).on('click', function() {
              window.location.href = '/pricelists/edit/' + data.id;
            });
          }
        });
      });
    </script>
  @endpush
</div>
