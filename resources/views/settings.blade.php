<x-app-layout>
  <x-slot name="header">
    <h2 class="font-semibold text-xl text-gray-800 leading-tight">
      {{ __('Instellingen') }}
    </h2>
  </x-slot>

  <div>
    <div class="max-w-7xl mx-auto py-10 sm:px-6 lg:px-8">
      <x-action-section>
        <x-slot name="title">
          {{ __('Maintenance mode') }}
        </x-slot>

        <x-slot name="description">
          {{ __('Staat de applicatie in Maintenance Mode.') }}
        </x-slot>

        <x-slot name="content">
          <p>
            Wanneer maintanance mode aanstaat dan zijn alle externe connecties tijdelijk uitgeschakeld.
          </p>
          <livewire:maintenance-setting />
        </x-slot>
      </x-action-section>
      <x-section-border />
      <x-action-section>
        <x-slot name="title">
          {{ __('Transmission Base Prijsafspraken') }}
        </x-slot>

        <x-slot name="description">
          {{ __('Basis prijsafspraken die gelden voor klanten die zelf geen prijsafspraak hebben.') }}
        </x-slot>

        <x-slot name="content">
          <p>
            Orders die geplaatst worden door klanten die geen afwijkende prijsafspraken hebben worden berekend volgens deze transportprijsstaffel.<br>
          </p>
          <a style="text-decoration: none" href="{{ route('transportagreement.edit', ['transportPriceAgreement' => 1]) }}" class="inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
            Basis Tranmission Prijs Staffel bewerken
          </a>
        </x-slot>
      </x-action-section>
      <x-section-border />
      <x-action-section>
        <x-slot name="title">
          {{ __('Transmission Transport kosten') }}
        </x-slot>

        <x-slot name="description">
          {{ __('De prijzen die Transmission ons rekent voor transport..') }}
        </x-slot>

        <x-slot name="content">
          <p>
            Bij iedere bestelling wordt naast de transportkosten voor de klant ook de transport inkoopkosten voor ons vastgelegd.
          </p>
          <a style="text-decoration: none" href="{{ route('transmissioncost.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
            Transmission Inkoop Prijzen
          </a>
        </x-slot>
      </x-action-section>
      <x-section-border />
      <x-action-section>
        <x-slot name="title">
          {{ __('Transmission Diesel Toeslag') }}
        </x-slot>

        <x-slot name="description">

        </x-slot>

        <x-slot name="content">
          <p>
            Diesel toeslag
          </p>
          <livewire:transmission-fuel-surcharge />
        </x-slot>
      </x-action-section>
      <x-section-border />
        <x-action-section>
            <x-slot name="title">
                {{ __('Transport Postcodes') }}
            </x-slot>

            <x-slot name="description">
                {{ __('Postcode ranges voor Transmission') }}
            </x-slot>

            <x-slot name="content">
                <p>
                    Transmission rekent andere tarieven voor Belgie, Zeeuws Vlaanderen, Noord Nederland en de Wadden Eilanden.
                    Bij het toevoegen van een range is het van belang om het volgende formaat aan te houden: '9100-9110'
                </p>
                <livewire:postalcode-settings />
            </x-slot>
        </x-action-section>
        <x-section-border />
      <x-action-section>
        <x-slot name="title">
          {{ __('Transmission Login') }}
        </x-slot>

        <x-slot name="description">

        </x-slot>

        <x-slot name="content">
          <p>
            Login gegevens voor Transmission API.
          </p>
          <livewire:transmission-login-data />
        </x-slot>
      </x-action-section>
      <x-section-border />
      <x-action-section>
        <x-slot name="title">
          {{ __('Transus Login') }}
        </x-slot>

        <x-slot name="description">

        </x-slot>

        <x-slot name="content">
          <p>
            Login gegevens voor Transus SOAP.
          </p>
          <livewire:transus-login-setting />
        </x-slot>
      </x-action-section>
      <x-section-border />
      <x-action-section>
        <x-slot name="title">
          {{ __('API Bearer Token') }}
        </x-slot>

        <x-slot name="description">

        </x-slot>

        <x-slot name="content">
          <livewire:user-api-token />
        </x-slot>
      </x-action-section>
      <x-section-border />
    </div>
  </div>
</x-app-layout>
