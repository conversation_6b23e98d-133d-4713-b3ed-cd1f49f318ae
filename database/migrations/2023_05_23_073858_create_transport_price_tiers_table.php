<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('transport_price_tiers', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('transport_price_agreement_id');
            $table->integer('trailer_spot');
            $table->float('price_nl')->comment('Price Nederland');
            $table->float('price_be')->comment('Prijs Belgie');
            $table->float('price_zv')->comment('Prijs Zeeuws Vlaanderen');
            $table->float('price_we')->comment('Prijs Wadden Eilanden');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transport_price_tiers');
    }
};
