<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoice_items', function (Blueprint $table) {
            $table->float('article_net_price');
            $table->string('article_unit_code');
            $table->string('article_code_supplier');
            $table->float('vat_base_amount');
            $table->float('vat_percentage');
            $table->integer('gtin');
            $table->float('invoiced_qty');
            $table->float('net_line_amount');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoice_items', function (Blueprint $table) {
            //
        });
    }
};
