<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('transmission_product_data', function (Blueprint $table) {
            $table->id();
            $table->integer('product_id');
            $table->integer('transport_weight');
            $table->integer('length');
            $table->integer('width');
            $table->integer('height');
            $table->string('description')->nullable();
            $table->string('unit_type');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transmission_product_data');
    }
};
