<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('credit_orderlines', function (Blueprint $table) {
            $table->unsignedInteger('credit_order_id');
            $table->unsignedInteger('order_item_id');
            $table->float('credit_amount')->default(0.00);
            $table->float('qty');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('credit_orderlines', function (Blueprint $table) {
            //
        });
    }
};
