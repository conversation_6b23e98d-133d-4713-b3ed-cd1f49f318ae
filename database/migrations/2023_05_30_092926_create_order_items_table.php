<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_items', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('order_id');
            $table->unsignedInteger('product_id');
            $table->string('sku');
            $table->float('qty');
            $table->string('product_name');
            $table->string('product_desc')->nullable();
            $table->float('cost')->default(0.00);
            $table->float('price_ex_vat')->default(0.00);
            $table->float('price_incl_vat')->default(0.00);
            $table->float('vat')->default(0.00);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_items');
    }
};
