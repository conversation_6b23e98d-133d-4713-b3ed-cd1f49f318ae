<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('transmission_costs', function (Blueprint $table) {
            $table->id();
            $table->float('trailer_length');
            $table->float('price_nl');
            $table->float('price_be');
            $table->float('price_zv');
            $table->float('price_we');
            $table->float('price_north');
            $table->timestamp('from_date')->nullable();
            $table->timestamp('to_date')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transmission_costs');
    }
};
