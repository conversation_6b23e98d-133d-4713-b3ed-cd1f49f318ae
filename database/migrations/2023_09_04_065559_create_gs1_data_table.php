<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('gs1_data', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('product_id');
            $table->string('target_sector');
            $table->string('gpc');
            $table->timestamp('valid_from');
            $table->timestamp('available_from');
            $table->string('tax_code');
            $table->string('tax_category');
            $table->string('packaging_code');
            $table->string('brand_name');
            $table->string('product_name');
            $table->string('short_name');
            $table->text('description');
            $table->float('net_content');
            $table->float('height');
            $table->float('width');
            $table->float('depth');
            $table->float('weight');
            $table->boolean('smallest_base_unit');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('gs1_data');
    }
};
