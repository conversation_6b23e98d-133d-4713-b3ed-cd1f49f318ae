<?php
declare(strict_types=1);

namespace Database\Factories;

use App\Models\Customer;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use Faker\Factory as FakerFactory;

class CustomerFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Customer::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {

        $faker = FakerFactory::create('nl_NL');  // Use locale 'nl_NL'

        return [
            'exact_id' => $faker->unique()->randomNumber(),
            'name' => $faker->name,
            'address' => $faker->streetAddress,
            'postal_code' => $faker->postcode,
            'city' => $faker->city,
            'country' => 'Netherlands',
            'email' => $faker->unique()->safeEmail,
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
}